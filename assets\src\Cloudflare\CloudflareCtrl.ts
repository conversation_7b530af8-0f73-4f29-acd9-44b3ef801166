import { ALL_APP_SOURCE_CONFIG } from "../Config";
import Global from "../GlobalScript";
import NetService from "../net/NetService";
import TimeLock from "../utils/TimeLock";
import CloudflareVerify from "./CloudflareVerify";

/** CF 验证场景(需和后端保持同步配置) */
export enum CloudFlareScene {
    /** 无 */
    NONE = '',
    /** 手机号注册登录-获取验证码 */
    LOGIN_PHONE_GET_CODE = 'SCENE_GET_CODE',
    /** 登录-提交 */
    LOGIN_SUBMIT = 'SCENE_LOGIN',
    /** 忘记密码-获取验证码 */
    FORGET_PW_GET_CODE = 'SCENE_FORGET_PW_GET_CODE',
    /** 忘记密码-提交 */
    FORGET_PW_SUBMIT = 'SCENE_FORGET_PASSWORD',
    /** 首次设定登录密码 */
    FIRST_SET_LOGIN_PW = 'SCENE_FIRST_PASSWORD',
    /** 首次设定支付密码 */
    FIRST_SET_PAY_PW = 'SCENE_FIRST_PAY_PASSWORD',
    /** 修改登录密码-获取验证码 */
    MODIFY_LOGIN_PW_GET_CODE = 'SCENE_MODIFY_LOGIN_PW_GET_CODE',
    /** 修改登录密码-提交 */
    MODIFY_LOGIN_PW_SUBMIT = 'SCENE_CHANGE_PASSWORD',
    /** 修改支付密码-获取验证码 */
    MODIFY_PAY_PW_GET_CODE = 'xxx',
    /** 修改支付密码-提交 */
    MODIFY_PAY_PW_SUBMIT = 'SCENE_CHANGE_PAY_PASSWORD',
    /** 绑定提款账号-获取验证码 */
    BIND_WITHDRAWAL_ACCOUNT_GET_CODE = 'xxx',
    /** 绑定提款账号-提交 */
    BIND_WITHDRAWAL_ACCOUNT_SUBMIT = 'SCENE_BIND_WITHDRAW_ACCOUNT',
    /** 修改提款账号-获取验证码 */
    MODIFY_WITHDRAWAL_ACCOUNT_GET_CODE = 'xxx',
    /** 修改提款账号-提交 */
    MODIFY_WITHDRAWAL_ACCOUNT_SUBMIT = 'SCENE_CHANGE_WITHDRAW_ACCOUNT',
    /** 提现-提交订单 */
    WITHDRAWAL_SUBMIT = 'SCENE_WITHDRAW',
    /** 绑定手机号-获取验证码 */
    BIND_PHONE_GET_CODE = 'xxx',
    /** 绑定手机号-提交 */
    BIND_PHONE_SUBMIT = 'SCENE_BIND_PT_PHONE',
    /** 修改手机号-获取验证码 */
    MODIFY_PHONE_GET_CODE = 'SCENE_MODIFY_PHONE_GET_CODE',
    /** 修改手机号-提交 */
    MODIFY_PHONE_SUBMIT = 'SCENE_CHANGE_PT_PHONE',
    /** KYC 提交 */
    KYC_SUBMIT = 'SCENE_SUB_KYC_INFO',
}

export default class CloudflareCtrl {
    /** 实例 */
    private static _instance: CloudflareCtrl;

    /** 获取实例 */
    public static get instance() {
        if (!CloudflareCtrl._instance) {
            CloudflareCtrl._instance = new CloudflareCtrl();
        }
        return CloudflareCtrl._instance;
    }

    /** 上一次请求的场景 */
    private _lastScene = CloudFlareScene.NONE;
    public get lastScene() {
        return this._lastScene;
    }

    /** 上一次请求到的 token */
    private _lastToken = '';
    public get lastToken() {
        return this._lastToken;
    }

    /** cf 验证信息映射表 */
    /** 先写死 */
    private _cfInfoMap = {
        "SCENE_LOGIN": {
            mode: "managed",
            sitekey: "0x4AAAAAABr6liO_iAPr4Zx_"
        },
        "SCENE_GET_CODE": {
            mode: "invisible",
            sitekey: "0x4AAAAAABr6n02z8VbwKkph"
        },

    };

    /** 操作锁 */
    private _opeLock = new TimeLock(3000);

     /** 消费 token */
    public consumeToken() {
        if (this._lastScene && this._lastToken) {
            // token 消费掉就清空，防止重复使用
            const reuslt = { scene: this._lastScene, token: this._lastToken };
            this._lastScene = CloudFlareScene.NONE;
            this._lastToken = '';
            return reuslt;
        }
        return null;
    }

    /**
     * 获取验证 token
     * @param scene 场景
     * @param cb 回调
     */
    public async getVerifyToken(scene: CloudFlareScene, cb: Function) {
        // if (Global.getInstance().loginVerifyType !== 2) {
        //     // 未开启 CF 验证
        //     cb?.({ token: '', code: 0 });
        //     return;
        // }

        if (this._opeLock.lock) {
            return;
        }

        const popNode = Global.getInstance().popNode;
        if (!popNode) {
            return;
        }

        if (popNode.getChildByName('CloudflareVerify')) {
            return;
        }

        // 锁定操作
        this._opeLock.lock = true;

        // 清理 token 数据
        this._lastScene = CloudFlareScene.NONE;
        this._lastToken = '';

        // if (!this._cfInfoMap || !this._cfInfoMap[scene]) {
        //     try {
        //         const res = await NetService.instance.postRequest('/cloudflare/v1/turnstile/keys', {});
        //         if (res?.code === 200 && res?.data) {
        //             this._cfInfoMap = {};
        //             for (let index = 0; index < res.data.length; index++) {
        //                 const item = res.data[index];
        //                 this._cfInfoMap[item.scene] = item;
        //             }
        //         } else {
        //             cb?.({ token: '', code: 0 });
        //             Global.getInstance().showSimpleTip(Global.getInstance().getLabel("tipword101"));
        //             return;
        //         }
        //     } catch (error) {
        //         // 清理验证数据，下次重新拉取
        //         this._cfInfoMap = null;
        //         cb?.({ token: '', code: 0 });
        //         Global.getInstance().showSimpleTip(Global.getInstance().getLabel("tipword101"));
        //         return;
        //     }
        // }

        // 加载预制体
        cc.resources.load('prefab/Cloudflare/CloudflareVerify', cc.Prefab, (err, prefab: cc.Prefab) => {
            if (!prefab) {
                // 加载预制体失败
                // 解锁操作
                this._opeLock.lock = false;
                // 飘字提醒
                Global.getInstance().showSimpleTip(Global.getInstance().getLabel("tipword101"));
                console.error('CloudflareVerify load prefab error: ', err);
                cb?.({ token: '', code: 1 });
                return;
            }

            // 再次锁定操作
            this._opeLock.lock = true;

            // // ------------------------------ 测试数据，测试完成后需要删除本段代码，把下方注释代码打开 start ------------------------------ //
            // this._cfInfoMap = {};
            // this._cfInfoMap[scene] = {
            //     scene,
            //     mode: 'managed',
            //     sitekey: ALL_APP_SOURCE_CONFIG.CloudflareVerify_SITE_KEY[ALL_APP_SOURCE_CONFIG.debug_mode_main],
            // };
            // // ------------------------------ 测试数据，测试完成后需要删除本段代码，把下方注释代码打开 end ------------------------------ //
            const cfInfo = this._cfInfoMap[scene];
            const appearance = 'always';
            const autoClose = cfInfo.mode === 'invisible';

            const node = cc.instantiate(prefab);
            popNode.addChild(node);
            const cmp = node.getComponent(CloudflareVerify);
            cmp.tokenCB = (token: string) => {
                this._opeLock.lock = false;

                if (!token) {
                    // 清理当前场景的验证信息
                    // this._cfInfoMap[scene] = null;

                    // 验证失败，飘字提醒
                    Global.getInstance().showSimpleTip('Verification failed.');
                }

                // 记录 token 数据
                this._lastScene = scene;
                this._lastToken = token;

                // 调用回调
                cb?.({ token, code: token ? 0 : 2 });
            }
            cmp.show(cfInfo.sitekey, appearance, autoClose);
        });
    }
}
