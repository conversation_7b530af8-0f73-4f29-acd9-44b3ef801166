/**
 * create by lemoon coo
 * kyc 各种状态弹窗
 */
import UICommon from "../component/UICommon";
import { DEEP_INDEXZ, UI_PATH_DIC } from "../GlobalConstant";
import { uiManager } from "../mgr/UIManager";
import { E_KYC_EDIT_TYPE } from "./EditItem";
import { INGSME_TYPE, KYC_STATE, KycMgr } from "./KycMgr";

const { ccclass, property } = cc._decorator;

@ccclass
export default class KycTipsWithdral extends UICommon {
    //提示信息
    @property(cc.Label)
    message: cc.Label = null;

    //提示信息2
    @property(cc.Label)
    message2: cc.Label = null;

    //标题
    @property(cc.Label)
    title: cc.Label = null;

    //详细信息
    @property(cc.Label)
    word: cc.Label = null;

    @property(cc.Node)
    yesbtn: cc.Node = null;

    @property(cc.Node)
    nobtn: cc.Node = null;

    confirmfun = null;
    cancelfun = null;
    target = null;


    onLoad(): void {
        
    }
    init(args: any) {
        // 状态0未认证 1已认证 2审核中 3拒绝 
        this.message.node.active = false;
        this.message2.node.active = false;
        if(args.params.kyc_step == 0){
            this.message.node.active = true;
            this.message.string = 'Your account is not yet fully verified!'
        }else if(args.params.kyc_step == 2){
            this.message2.node.active = true;
        }else if(args.params.kyc_step == 3){
            this.message.node.active = true;
            this.message.string = 'Review failed!'
        }
        if(args.params.kyc_status == 2){
            if(args.params.kyc_step == 2){
                //这里跳到kyc审核弹窗
                KycMgr.instance.review_pop();
                this.hide();
            }else{
                this.yesbtn.active = false;
                this.nobtn.x = 0;
            }
        }else{
            //前两单这是
            this.nobtn.active = false;
            this.yesbtn.x = 0;
        }
        this.confirmfun = args.params.confirmfun;
        this.target = args.params.target;
    }
    verfiry_now(){
        //关闭自己 打开kyc
        uiManager.instance.showDialog(UI_PATH_DIC.KYCVerification, [{ status: KycMgr.instance.kyc_simple}], null, DEEP_INDEXZ.KYC);
        this.hide();
    }
    continue(){
        this.confirmfun && this.confirmfun()
        // if (this.confirmfun !== null && this.confirmfun !== undefined) {
        //     // this.confirmfun.call(this.target, this.node.name);

        // }
        this.hide()
    }
    refreshBtn(params?){
       
    }
    onDestroy(): void {
       
    }

    closeDialog() {
        if (this.nobtn.active) {
            this.closeCommonTips()
        }
    }

    closeCommonTips() {
        this.hide()
    }

    confirmClick() {
        if (this.confirmfun !== null && this.confirmfun !== undefined) {
            this.confirmfun.call(this.target, this.node.name);
        }
        if (!!this.node && this.node.isValid) {
            this.closeCommonTips()
        }
    }

    cancelClick() {
        if (this.cancelfun !== null && this.cancelfun !== undefined) {
            this.cancelfun.call(this.target);
        }
        this.closeCommonTips()
    }
}
