import { CLIENTLOGKEY, ClientLogManager } from "../ClientLogConfis";
import { ALL_APP_SOURCE_CONFIG } from "../Config";
import { CHANEL_PARAM, E_CHANEL_TYPE, E_SCENE_TYPE } from "../GlobalConstant";
import Global from "../GlobalScript";
// import { ALL_APP_SOURCE_CONFIG } from "../Config";

/**
 * 需要进行 CF 验证的请求名
 * 这些请求需要将 cf-token 放到请求头中
 */
const CF_TOKEN_CMD_MAP = {
    '/common/api/sms/send/short/msg': true,
    '/common/api/player/login': true,
};

export default class HttpUtils {
    // reqTime:number = 0;//记录请求次数
    reRry: number = 3; //重连次数
    static instance: HttpUtils = null;
    private weakFunc: any = null;

    static getInstance() {
        if (HttpUtils.instance == null) {
            HttpUtils.instance = new HttpUtils();
        }
        return HttpUtils.instance;
    }

    getHost() {
        if (window['hostipstring'] && window['hostipstring'].indexOf('http') != -1) {
            return window['hostipstring']
        }
        let url = ALL_APP_SOURCE_CONFIG.app_hosts[Global.DEBUG];

        // let strArr = Global.getInstance().getAppBundleId().split(".");
        // console.log("strArr ", strArr)
        // if (strArr.length > 2) {
        //     return url = url + "/" + strArr[2];
        // }
        return url;
    }

    appendParams(paramsStr, key, value) {
        let ret = paramsStr;
        if (ret.length > 0) {
            ret += "&";
        }
        ret += key;
        ret += "=";
        ret += encodeURIComponent(value);
        return ret;
    }

    //执行一次请求,type 0:不能失败（重连必定转圈），1 可以失败（重连不转圈），2 可以失败（重连转圈, 3 可以失败（请求开始就转圈）
    async RequestOnce(type: Number, target: any, url: string, params: Object, times: any, successCallback?: (response) => void | null, errorCallback?: (response) => void | null) {
        return new Promise((resolve, reject) => {
            let onTimeout = () => {
                // reject('请求超时')
                console.log("请求超时")
                Global.getInstance().hideShowLoading(url);//暂时先这么改
                if (times == this.reRry) {
                    if (type == 2) {
                        Global.getInstance().showLoading(url);
                    } else if (type == 4) {
                        //目前4的情况 充值用到 超时
                        !!errorCallback && errorCallback.call(target);
                    }
                } else if (times == 0) {
                    !!errorCallback && errorCallback.call(target);
                    if (type == 0) {
                        Global.getInstance().onErrorCallback({ "http": url });
                    } else if (type != 1) {
                        Global.getInstance().hideShowLoading(url);
                    }
                }
            }
            let onError = () => {
                // reject('请求失败')
                console.log("请求失败", url)
                Global.getInstance().hideShowLoading(url);//暂时先这么改
                !!errorCallback && errorCallback.call(target);
                if (type == 0) {
                    Global.getInstance().onErrorCallback({ "http": url });
                } else if (type != 1) {
                    Global.getInstance().hideShowLoading(url);
                }
            }

            let xhr = new XMLHttpRequest();
            xhr.onreadystatechange = () => {
                if (4 == xhr.readyState) {
                    this.stopWeakSchedule();//清掉弱网定时器
                    if (xhr.status >= 200 && xhr.status < 300) {
                        // resolve(xhr.response);
                        // console.log("response = " + xhr.response);
                        let data = JSON.parse(xhr.response);
                        if ((type != 1 && type != 0) || (type == 0 && data["code"] != 200)) {
                            Global.getInstance().hideShowLoading(url);
                        }
                        if (data["msgBox"] && data["msgBox"].length > 0) {
                            Global.getInstance().addToMsgBox(data["msgBox"])
                        }
                        if (data["code"] == 200 || data["code"] == 0) {
                            successCallback && successCallback.call(target, data);
                        } else if (data["code"] == 400 || data["code"] == 102010 || data["code"] == 100010 || data["code"] == 401000000) {// token过期 清理 重启
                            // Global.getInstance().clearToken();
                            if (Global.getInstance().getSceneId() == E_SCENE_TYPE.LOGIN) {
                                !!errorCallback && errorCallback.call(target, data);
                                Global.getInstance().hideShowLoading(url);
                            } else {
                                //Global.getInstance().onErrorCallback();
                                console.log("response = URL - relogin " + url);
                                console.log("response = relogin " + xhr.response);
                                if (url != '/vdr/api/sports/boxing') {
                                    //拳皇直播的接口 会反复请求数据 防止反复提示
                                    this.reLogin();
                                }
                            }
                        } else {
                            if (!errorCallback) {
                                if (!!data["msg"] && data["msg"] != "") {
                                    Global.getInstance().showSimpleTip(data["msg"]);
                                } else {
                                    Global.getInstance().showSimpleTip(Global.getInstance().getLabel("php_code_" + data["code"]));
                                }
                            } else {
                                errorCallback.call(target, data);
                            }
                        }
                    } else {
                        console.log("请求失败", url, xhr.status, "--", JSON.stringify(xhr));
                        if (xhr.status == 500 || xhr.status == 403) {// 未知错误
                            !!errorCallback && errorCallback.call(target, { code: xhr.status });
                            Global.getInstance().hideShowLoading(url);
                        }
                        if (xhr.status == 401) {// token过期 清理 重启
                            // Global.getInstance().clearToken();
                            if (Global.getInstance().getSceneId() == E_SCENE_TYPE.LOGIN) {
                                !!errorCallback && errorCallback.call(target, { code: xhr.status });
                                Global.getInstance().hideShowLoading(url);
                            } else {
                                Global.getInstance().onLoggedErrorCallback();
                                //this.reLogin();
                            }
                        }
                        if (times == 0) {
                            ClientLogManager.upLoadSingleLogs(CLIENTLOGKEY.HttpError, { error: "请求失败", params: params, url: url })
                            !!errorCallback && errorCallback.call(target);
                            if (type == 0) {
                                Global.getInstance().onErrorCallback({ "http": url });
                            } else if (type != 1) {
                                Global.getInstance().hideShowLoading(url);
                            }
                        }
                    }
                }
            };

            let requestUrl = this.getHost() + url;
            if (url.indexOf('https://') >= 0 || url.indexOf('http://') >= 0) {
                requestUrl = url;
            }
            params["action"] = url;

            xhr.open("POST", requestUrl, true);
            xhr.setRequestHeader("Content-Type", "application/x-www-form-urlencoded");
            let terminal_str = CHANEL_PARAM[ALL_APP_SOURCE_CONFIG.channel] + ''
            xhr.setRequestHeader("terminal", terminal_str);//统一加上header
            if (url == "/vdr/api/jili/login") { //这里是暂时这么加的 因为会走不同的环境
                let companyId = params["company_id"];
                let name = Global.getInstance().getGameProviderNameById(companyId);
                let nametrim = name.replace(/\s+/g, '');
                xhr.setRequestHeader("CaType", nametrim);
            }
            if (Global.getInstance().userdata && Global.getInstance().userdata.grayscale) {
                xhr.setRequestHeader("GrayScale", Global.getInstance().userdata.grayscale);
            }
            if (Global.getInstance().token) {
                xhr.setRequestHeader("token", Global.getInstance().token);
            }
            // 拼接参数
            let paramsStr = "";
            for (let key in params) {
                //geetest 如果是空参数 直接去除这三个参数
                let threeparam = ['geetest_guard', 'userInfo', 'geetest_captcha']
                let isremove = false;//是否需要去除
                if (threeparam.indexOf(key) != -1) {
                    if (params[key] == '') {
                        isremove = true
                    }
                }
                if (key == 'buds') {
                    params[key] = CHANEL_PARAM[ALL_APP_SOURCE_CONFIG.channel] + ''
                }
                //以上是geetest 修改的bug
                if (!isremove) {
                    paramsStr = this.appendParams(paramsStr, key, params[key]);
                }
            }
            xhr.timeout = (type == 4 ? 3000 : 20000);// 超时设置为20s
            // xhr.timeout = 20000
            // 网络层的超时与错误处理统一走超时错误
            xhr.ontimeout = onTimeout;
            xhr.onerror = onError;
            // console.log("requestParams = " + requestUrl + " " + paramsStr);
            xhr.send(paramsStr);
        })
    }
    async RequestMoreTry(type: Number, target: any, url: string, params: Object, times: any, successCallback?: (response) => void | null, errorCallback?: (response) => void | null) {
        // console.error('执行' + url + '请求，请求第' + (++times) + '次尝试')
        // try {
        //     const response_1 = await this.RequestOnce(type, target, url, params, times, successCallback, errorCallback);
        //     Promise.resolve(response_1);
        // } catch (err) {
        //     let timesRetry = times > 0;
        //     console.error('请求失败', err);
        //     timesRetry ? this.RequestMoreTry(type, target, url, params, times - 1, successCallback, errorCallback) : Promise.reject(err);
        // }
        this.RequestOnce(type, target, url, params, times, successCallback, errorCallback)
        // .then(() => {
        //     console.log("请求成功")
        // })
        // .catch((err) => {
        //     let timesRetry = times > 0;
        //     console.error('请求失败', err);
        //     if (timesRetry) this.RequestMoreTry(type, target, url, params, times - 1, successCallback, errorCallback);
        // })
    }

    /**
     * 
     * @param type 
     * @param reRry 
     * @param target 
     * @param url 
     * @param params 
     * @param successCallback 
     * @param errorCallback 
     */
    post(type: Number, reTry: Number, target: any, url: string, params: Object, successCallback?: (response) => void | null, errorCallback?: (response) => void | null) {
        // this.reqTime = 0;
        let isWeakNetwork = false;//弱网标志
        if (type == 0 || type == 3 || type == 4) {
            Global.getInstance().showLoading(url, isWeakNetwork);
            this.weakNetFunc(url);
        }
        this.RequestMoreTry(type, target, url, params, reTry, successCallback, errorCallback);
    }

    get(type: Number, reTry: Number, target: any, url: string, params: Object, successCallback?: (response) => void | null, errorCallback?: (response) => void | null) {
        // this.reqTime = 0;
        let isWeakNetwork = false;//弱网标志
        if (type == 0 || type == 3 || type == 4) {
            Global.getInstance().showLoading(url, isWeakNetwork);
            this.weakNetFunc(url);
        }
        this.RequestMoreGetTry(type, target, url, params, reTry, successCallback, errorCallback);
    }

    /**
     * 弱网5s 超时后给出提示并隐藏 loading加载页
     * @param url 
     */
    weakNetFunc(url) {
        let weakFun = () => {
            Global.getInstance().hideShowLoading(url);
            Global.getInstance().showSimpleTip("The network signal is weak.");
        };
        this.stopWeakSchedule();
        if (!this.weakFunc) {
            this.weakFunc = setTimeout(weakFun, 8000);
        }
    }

    /**
     * 清掉弱网定时器
     */
    stopWeakSchedule() {
        if (this.weakFunc) {
            clearTimeout(this.weakFunc);
            this.weakFunc = null;
        }
    }

    //接口token过期 需要退出到登录页面 的逻辑
    reLogin() {
        let string1 = "Your login has expired, please\nlog in again"
        if (Global.instance.is_mini_game()) {
            string1 = "Since you have not operated on for a long time, please log in again."
        }
        Global.getInstance().showCommonTip2({ word: string1, confirm: "OK" }, this, true, () => {
            //下一次进入游戏 弹出登陆页面
            window["isShowLoginPage"] = 1;
            Global.instance.logout(false);
        }, null, null, "Tips");
    }
    //执行一次请求,type 0:不能失败（重连必定转圈），1 可以失败（重连不转圈），2 可以失败（重连转圈, 3 可以失败（请求开始就转圈）
    //4 可以失败 请求转圈 超时3秒
    async RequestMoreGetTry(type: Number, target: any, url: string, params: Object, times: any, successCallback?: (response) => void | null, errorCallback?: (response) => void | null) {
        return new Promise((resolve, reject) => {
            let onTimeout = () => {
                // reject('请求超时')
                console.log("请求超时")
                if (times == this.reRry) {
                    if (type == 2) {
                        Global.getInstance().showLoading(url);
                    }
                } else if (times == 0) {
                    !!errorCallback && errorCallback.call(target);
                    if (type == 0) {
                        Global.getInstance().onErrorCallback({ "http": url });
                    } else if (type != 1) {
                        Global.getInstance().hideShowLoading(url);
                    }
                } else {
                    !!errorCallback && errorCallback.call(target);
                }
            }
            let onError = () => {
                // reject('请求失败')
                console.log("请求失败", url)
                !!errorCallback && errorCallback.call(target);
                if (type == 0) {
                    Global.getInstance().onErrorCallback({ "http": url });
                } else if (type != 1) {
                    Global.getInstance().hideShowLoading(url);
                }
            }

            let xhr = new XMLHttpRequest();
            xhr.onreadystatechange = () => {
                if (4 == xhr.readyState) {
                    this.stopWeakSchedule();
                    if (xhr.status >= 200 && xhr.status < 300) {
                        // resolve(xhr.response);
                        // console.log("response = " + xhr.response);
                        let data = JSON.parse(xhr.response);
                        // console.log("onreadystatechange ==>", type, data["code"])
                        if ((type != 1 && type != 0) || (type == 0 && data["code"] != 200)) {
                            Global.getInstance().hideShowLoading(url);
                        }
                        if (data["msgBox"] && data["msgBox"].length > 0) {
                            Global.getInstance().addToMsgBox(data["msgBox"])
                        }
                        if (data["code"] == 200 || data["code"] == 0) {
                            successCallback && successCallback.call(target, data);
                        } else if (data["code"] == 400 || data["code"] == 102010 || data["code"] == 100010 || data["code"] == 401000000) {// token过期 清理 重启
                            // Global.getInstance().clearToken();
                            if (Global.getInstance().getSceneId() == E_SCENE_TYPE.LOGIN) {
                                !!errorCallback && errorCallback.call(target, data);
                                Global.getInstance().hideShowLoading(url);
                            } else {
                                //Global.getInstance().onErrorCallback();
                                console.log('----------连接:', url)
                                this.reLogin();
                            }
                        } else {
                            if (!errorCallback) {
                                if (!!data["msg"] && data["msg"] != "") {
                                    Global.getInstance().showSimpleTip(data["msg"]);
                                } else {
                                    Global.getInstance().showSimpleTip(Global.getInstance().getLabel("php_code_" + data["code"]));
                                }
                            } else {
                                errorCallback.call(target, data);
                            }
                        }
                    } else {
                        console.log("请求失败", xhr.status, "  ", xhr.statusText, url);
                        if (xhr.status == 500 || xhr.status == 403) {// token过期 清理 重启
                            !!errorCallback && errorCallback.call(target, { code: xhr.status });
                            Global.getInstance().hideShowLoading(url);
                        }
                        if (xhr.status == 401) {// token过期 清理 重启
                            // Global.getInstance().clearToken();
                            if (Global.getInstance().getSceneId() == E_SCENE_TYPE.LOGIN) {
                                !!errorCallback && errorCallback.call(target, {});
                                Global.getInstance().hideShowLoading(url);
                            } else {
                                Global.getInstance().onLoggedErrorCallback();
                                //this.reLogin(); 
                            }
                        }
                        if (times == 0) {
                            ClientLogManager.upLoadSingleLogs(CLIENTLOGKEY.HttpError, { error: "请求失败", params: params, url: url })
                            !!errorCallback && errorCallback.call(target);
                            if (type == 0) {
                                Global.getInstance().onErrorCallback({ "http": url });
                            } else if (type != 1) {
                                Global.getInstance().hideShowLoading(url);
                            }
                        }
                    }
                }
            };

            let requestUrl = this.getHost() + url + "?";
            if (url.indexOf('https://') >= 0 || url.indexOf('http://') >= 0) {
                requestUrl = url + '?';
            }
            // console.log("requestParams = " + requestUrl + " " + JSON.stringify(params));
            // 拼接参数
            let paramsStr = "";
            for (let key in params) {
                paramsStr = this.appendParams(paramsStr, key, params[key]);
            }
            xhr.open("GET", requestUrl + paramsStr, true);
            let terminal_str = CHANEL_PARAM[ALL_APP_SOURCE_CONFIG.channel] + ''
            xhr.setRequestHeader("terminal", terminal_str);//统一加上header
            xhr.setRequestHeader("Content-Type", "application/json");
            if (Global.getInstance().userdata && Global.getInstance().userdata.grayscale) {
                xhr.setRequestHeader("GrayScale", Global.getInstance().userdata.grayscale);
            }
            if (Global.getInstance().token) {
                xhr.setRequestHeader("token", Global.getInstance().token);
            }
            if (CF_TOKEN_CMD_MAP[url] && typeof(params["cf-token"]) === 'string' && params["cf-scene"]) {
                // cf 验证信息
                xhr.setRequestHeader("cf-token", params["cf-token"]);
                xhr.setRequestHeader("cf-scene", params["cf-scene"]);
            }
            xhr.timeout = (type == 4 ? 3000 : 20000);// 超时设置为20s
            // xhr.timeout = 20000
            // 网络层的超时与错误处理统一走超时错误
            xhr.ontimeout = onTimeout;
            xhr.onerror = onError;
            xhr.send();
        })
    }

    download(url: string, successCallback?: (response) => void | null, failCallback?: (err) => void | null): void {
        // let xhr = cc.loader.getXMLHttpRequest();
        let xhr = new XMLHttpRequest();
        xhr.responseType = "arraybuffer";
        xhr.onreadystatechange = () => {
            if (xhr.readyState === 4) {
                if (xhr.status === 200) {
                    successCallback && successCallback(xhr.response);
                }
                else {
                    failCallback && failCallback(null);
                }
            }
        };
        console.log("requestUrl = " + url);
        xhr.open("GET", url, true);

        xhr.timeout = 30000;
        xhr.send();
    }
}

(<any>window).HttpUtils = HttpUtils;
