import { E_CHANEL_TYPE } from "./GlobalConstant";

export const ALL_APP_SOURCE_CONFIG = {
    app_hosts: {
        DEBUG_MODE_DEV_PZV:"https://dev.nustaronline.vip",
        DEBUG_MODE_TEST_PZV:"https://test.nustaronline.vip",
        DEBUG_MODE_PRE_PZV:"https://pre.nustaronline.vip",
        DEBUG_MODE_RELEASE_PZV:"https://io.nustargame.com",
    },
      /** CF 验证 key */
    CloudflareVerify_SITE_KEY: {
        DEV: "0x4AAAAAABmR_Jwyy1tet7uX",
        TEST: "0x4AAAAAABnByxp2v1NuTm7f",
        PRE: "0x4AAAAAABpKiQV8_G7FJy6p",
        RELEASE: "0x4AAAAAABpKiQV8_G7FJy6p",
    },
    /** 活动页所在域名 */
    ActivityPageHost: {
        DEV: "https://wayfocus.nustaronline.vip/",
        TEST: "https://wayfocus.nustaronline.vip/",
        PRE: "https://wayfocus.nustaronline.vip/",
        RELEASE: "https://wayfocus.nustargame.com/",
    },
    channel: E_CHANEL_TYPE.WEB,
    app_id: 10000,  
    source_version: 20250720,
    app_version: "*******",//这里是版本号 每次上线需要更改的地方
    writablePath: "bf10000-remote-asset",
    app_bundle_id: "com.playmate.playzone",
    remote_assets_test: "https://remote-assets.atm27.com/",
    remote_assets_prod: {
        DEBUG_MODE_DEV_PZV:"https://uat-nustar-static.nustaronline.vip/",
        DEBUG_MODE_TEST_PZV:"https://uat-nustar-static.nustaronline.vip/",
        DEBUG_MODE_PRE_PZV:"https://uat-nustar-static.nustaronline.vip/",
        DEBUG_MODE_RELEASE_PZV:"https://nustar-static.nustargame.com/"
    },
    android_download_url: "",
    geetest_key:'0'
};
// (<any>window).GlobalSourceConfig = ALL_APP_SOURCE_CONFIG;
