/**
 * Vue3 Global Store Example
 * 全局状态管理示例 - 使用 Pinia 或 Vuex
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type {
  UserData,
  PhoneVerificationParams,
  SmsVerifyParams,
  BindPhoneParams,
  ChangePhoneParams,
  ApiResponse,
  CloudflareController
} from './types'
import { LocalStorage, ErrorHandler } from './utils'

/**
 * 全局状态管理 Store
 */
export const useGlobalStore = defineStore('global', () => {
  // 状态
  const userdata = ref<UserData | null>(null)
  const token = ref<string>('')
  const loginVerifyType = ref<number>(2) // 0-不验证 1-极验 2-CF验证
  const isLoading = ref<boolean>(false)
  const cloudflareController = ref<CloudflareController | null>(null)

  // 计算属性
  const isLoggedIn = computed(() => !!token.value && !!userdata.value)
  const hasPhone = computed(() => !!userdata.value?.phone)

  // Actions
  
  /**
   * 显示提示信息
   * @param message 提示信息
   */
  const showTip = (message: string) => {
    // 这里可以集成 Toast 组件或其他提示组件
    console.log('Tip:', message)
    
    // 示例：使用浏览器原生 alert（实际项目中应该使用更好的 UI 组件）
    if (typeof window !== 'undefined') {
      // 可以集成 Element Plus、Ant Design Vue 等 UI 库的 Message 组件
      alert(message)
    }
  }

  /**
   * 显示/隐藏加载状态
   * @param loading 是否显示加载
   */
  const setLoading = (loading: boolean) => {
    isLoading.value = loading
  }

  /**
   * 设置用户数据
   * @param data 用户数据
   */
  const setUserData = (data: UserData | null) => {
    userdata.value = data
    if (data) {
      LocalStorage.setItem('userdata', data)
    } else {
      LocalStorage.removeItem('userdata')
    }
  }

  /**
   * 设置 token
   * @param newToken token
   */
  const setToken = (newToken: string) => {
    token.value = newToken
    if (newToken) {
      LocalStorage.setItem('token', newToken)
    } else {
      LocalStorage.removeItem('token')
    }
  }

  /**
   * 初始化 store
   */
  const initialize = () => {
    // 从本地存储恢复数据
    const savedUserData = LocalStorage.getItem<UserData | null>('userdata', null)
    const savedToken = LocalStorage.getItem<string>('token', '')
    
    if (savedUserData) {
      userdata.value = savedUserData
    }
    
    if (savedToken) {
      token.value = savedToken
    }
  }

  /**
   * 获取本地存储数据
   * @param key 键
   * @param defaultValue 默认值
   * @returns 存储的值
   */
  const getStorageData = <T>(key: string, defaultValue: T): T => {
    return LocalStorage.getItem(key, defaultValue)
  }

  /**
   * 设置本地存储数据
   * @param key 键
   * @param value 值
   */
  const setStorageData = (key: string, value: any) => {
    LocalStorage.setItem(key, value)
  }

  /**
   * 检查是否需要屏幕上移
   * @returns 是否需要上移
   */
  const needScreenUp = (): boolean => {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) && 
           window.innerHeight < 600
  }

  /**
   * 获取 Cloudflare 控制器
   * @returns Cloudflare 控制器实例
   */
  const getCloudflareController = (): CloudflareController => {
    if (!cloudflareController.value) {
      throw new Error('Cloudflare controller not initialized')
    }
    return cloudflareController.value
  }

  /**
   * 设置 Cloudflare 控制器
   * @param controller 控制器实例
   */
  const setCloudflareController = (controller: CloudflareController) => {
    cloudflareController.value = controller
  }

  // API 调用方法

  /**
   * 发送短信验证码
   * @param params 请求参数
   * @returns API 响应
   */
  const sendSms = async (params: PhoneVerificationParams): Promise<ApiResponse> => {
    try {
      setLoading(true)
      
      // 这里应该调用实际的 API
      // 示例实现
      const response = await fetch('/api/sms/send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token.value}`
        },
        body: JSON.stringify(params)
      })
      
      const result = await response.json()
      
      if (!response.ok) {
        throw new Error(result.message || 'Send SMS failed')
      }
      
      return result
    } catch (error) {
      ErrorHandler.logError(error, 'sendSms')
      throw error
    } finally {
      setLoading(false)
    }
  }

  /**
   * 验证短信验证码
   * @param params 请求参数
   * @returns API 响应
   */
  const verifySmsCode = async (params: SmsVerifyParams): Promise<ApiResponse> => {
    try {
      setLoading(true)
      
      const response = await fetch('/api/sms/verify', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token.value}`
        },
        body: JSON.stringify(params)
      })
      
      const result = await response.json()
      
      if (!response.ok) {
        throw new Error(result.message || 'Verify SMS code failed')
      }
      
      return result
    } catch (error) {
      ErrorHandler.logError(error, 'verifySmsCode')
      throw error
    } finally {
      setLoading(false)
    }
  }

  /**
   * 绑定手机号
   * @param params 请求参数
   * @returns API 响应
   */
  const bindPhone = async (params: BindPhoneParams): Promise<ApiResponse> => {
    try {
      setLoading(true)
      
      const response = await fetch('/api/phone/bind', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token.value}`
        },
        body: JSON.stringify(params)
      })
      
      const result = await response.json()
      
      if (!response.ok) {
        throw new Error(result.message || 'Bind phone failed')
      }
      
      // 更新用户数据
      if (result.data?.user_info) {
        setUserData(result.data.user_info)
      }
      
      return result
    } catch (error) {
      ErrorHandler.logError(error, 'bindPhone')
      throw error
    } finally {
      setLoading(false)
    }
  }

  /**
   * 修改手机号
   * @param params 请求参数
   * @returns API 响应
   */
  const changePhone = async (params: ChangePhoneParams): Promise<ApiResponse> => {
    try {
      setLoading(true)
      
      const response = await fetch('/api/phone/change', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token.value}`
        },
        body: JSON.stringify(params)
      })
      
      const result = await response.json()
      
      if (!response.ok) {
        throw new Error(result.message || 'Change phone failed')
      }
      
      // 更新用户数据
      if (result.data?.user_info) {
        setUserData(result.data.user_info)
      }
      
      return result
    } catch (error) {
      ErrorHandler.logError(error, 'changePhone')
      throw error
    } finally {
      setLoading(false)
    }
  }

  /**
   * 登出
   */
  const logout = () => {
    setUserData(null)
    setToken('')
    cloudflareController.value = null
    LocalStorage.clear()
  }

  // 返回状态和方法
  return {
    // 状态
    userdata,
    token,
    loginVerifyType,
    isLoading,
    
    // 计算属性
    isLoggedIn,
    hasPhone,
    
    // 方法
    showTip,
    setLoading,
    setUserData,
    setToken,
    initialize,
    getStorageData,
    setStorageData,
    needScreenUp,
    getCloudflareController,
    setCloudflareController,
    sendSms,
    verifySmsCode,
    bindPhone,
    changePhone,
    logout
  }
})

/**
 * 在 Vue 应用外部使用 store 的辅助函数
 */
export const useGlobalStoreOutside = () => {
  const store = useGlobalStore()
  return store
}
