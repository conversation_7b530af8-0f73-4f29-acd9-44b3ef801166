<template>
  <div class="example-usage">
    <h1>Vue3 Phone Verification Example</h1>
    
    <div class="button-group">
      <button @click="showSetPhoneNumber" class="demo-btn">
        Set Phone Number
      </button>
      
      <button @click="showChangePhoneNumber" class="demo-btn">
        Change Phone Number
      </button>
      
      <button @click="showForgetPassword" class="demo-btn">
        Forget Password
      </button>
      
      <button @click="showAddWithdrawAccount" class="demo-btn">
        Add Withdraw Account
      </button>
      
      <button @click="showCloudflareVerify" class="demo-btn">
        Test Cloudflare Verify
      </button>
    </div>

    <div v-if="result" class="result-display">
      <h3>Last Result:</h3>
      <pre>{{ JSON.stringify(result, null, 2) }}</pre>
    </div>

    <!-- Phone Number Verification Component -->
    <PhoneNumberVerification
      ref="phoneVerificationRef"
      :verify-type="currentVerifyType"
      :initial-phone="initialPhone"
      :on-success="onVerificationSuccess"
      :on-cancel="onVerificationCancel"
    />

    <!-- Cloudflare Controller Component -->
    <CloudflareController
      ref="cloudflareRef"
      :config="cloudflareConfig"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import PhoneNumberVerification, { VerifyType } from './PhoneNumberVerification.vue'
import CloudflareController, { CloudFlareScene } from './CloudflareController.vue'

// Refs
const phoneVerificationRef = ref<InstanceType<typeof PhoneNumberVerification>>()
const cloudflareRef = ref<InstanceType<typeof CloudflareController>>()
const currentVerifyType = ref<VerifyType>(VerifyType.SetPhoneNumber)
const initialPhone = ref('')
const result = ref<any>(null)

// Cloudflare configuration
const cloudflareConfig = reactive({
  ActivityPageHost: {
    PRE: 'https://example.com/',
    DEV: 'https://dev.example.com/',
    TEST: 'https://test.example.com/'
  },
  CloudflareVerify_SITE_KEY: {
    PRE: '0x4AAAAAABr6liO_iAPr4Zx_',
    DEV: '0x4AAAAAABr6liO_iAPr4Zx_',
    TEST: '0x4AAAAAABr6liO_iAPr4Zx_'
  },
  debug_mode_main: 'PRE'
})

// Methods
const showSetPhoneNumber = () => {
  currentVerifyType.value = VerifyType.SetPhoneNumber
  initialPhone.value = ''
  phoneVerificationRef.value?.show()
}

const showChangePhoneNumber = () => {
  currentVerifyType.value = VerifyType.ChangePhoneNumber
  initialPhone.value = '***********' // Example old phone
  phoneVerificationRef.value?.show()
}

const showForgetPassword = () => {
  currentVerifyType.value = VerifyType.ForgetPassword
  initialPhone.value = '***********' // Example phone
  phoneVerificationRef.value?.show()
}

const showAddWithdrawAccount = () => {
  currentVerifyType.value = VerifyType.AddWithdrawAccount
  initialPhone.value = ''
  phoneVerificationRef.value?.show()
}

const showCloudflareVerify = async () => {
  try {
    const cloudflareCtrl = cloudflareRef.value
    if (cloudflareCtrl) {
      const verifyResult = await cloudflareCtrl.getVerifyToken(CloudFlareScene.LOGIN_SUBMIT)
      result.value = {
        type: 'Cloudflare Verification',
        result: verifyResult
      }
    }
  } catch (error) {
    result.value = {
      type: 'Cloudflare Verification Error',
      error: error.message
    }
  }
}

const onVerificationSuccess = (data: any) => {
  result.value = {
    type: 'Phone Verification Success',
    verifyType: currentVerifyType.value,
    data
  }
  console.log('Verification successful:', data)
}

const onVerificationCancel = () => {
  result.value = {
    type: 'Phone Verification Cancelled',
    verifyType: currentVerifyType.value
  }
  console.log('Verification cancelled')
}
</script>

<style scoped>
.example-usage {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

h1 {
  text-align: center;
  color: #333;
  margin-bottom: 40px;
}

.button-group {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 40px;
}

.demo-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 16px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.demo-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.demo-btn:active {
  transform: translateY(0);
}

.result-display {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
  margin-top: 20px;
}

.result-display h3 {
  margin-top: 0;
  color: #495057;
}

.result-display pre {
  background: #ffffff;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 16px;
  overflow-x: auto;
  font-size: 14px;
  color: #495057;
  margin: 0;
}

/* Mobile responsive */
@media (max-width: 768px) {
  .example-usage {
    padding: 16px;
  }
  
  .button-group {
    grid-template-columns: 1fr;
  }
  
  .demo-btn {
    padding: 14px 20px;
    font-size: 15px;
  }
}
</style>
