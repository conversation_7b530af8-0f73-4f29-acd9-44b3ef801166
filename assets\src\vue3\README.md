# Vue3 Phone Verification Components

这是从 Cocos Creator TypeScript 代码转换而来的 Vue3 组件，用于手机号验证和 Cloudflare 验证功能。

## 文件结构

```
vue3/
├── CloudflareController.vue    # Cloudflare 验证控制器组件
├── PhoneNumberVerification.vue # 手机号验证组件
├── ExampleUsage.vue           # 使用示例
├── types.ts                   # TypeScript 类型定义
└── README.md                  # 说明文档
```

## 组件功能

### CloudflareController.vue
- Cloudflare Turnstile 验证集成
- 支持多种验证场景
- 自动处理验证流程
- 支持 iframe 和消息通信

### PhoneNumberVerification.vue
- 手机号输入和验证
- 短信验证码发送和验证
- 支持多种验证类型（绑定、修改、忘记密码等）
- 倒计时功能
- 响应式设计，支持移动端

## 使用方法

### 1. 安装依赖

```bash
npm install vue@^3.0.0 vue-i18n@^9.0.0
```

### 2. 基本使用

```vue
<template>
  <div>
    <!-- 手机号验证组件 -->
    <PhoneNumberVerification
      ref="phoneVerificationRef"
      :verify-type="VerifyType.SetPhoneNumber"
      :on-success="onSuccess"
      :on-cancel="onCancel"
    />

    <!-- Cloudflare 验证组件 -->
    <CloudflareController
      ref="cloudflareRef"
      :config="cloudflareConfig"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import PhoneNumberVerification, { VerifyType } from './PhoneNumberVerification.vue'
import CloudflareController, { CloudFlareScene } from './CloudflareController.vue'

const phoneVerificationRef = ref()
const cloudflareRef = ref()

const cloudflareConfig = {
  ActivityPageHost: {
    PRE: 'https://your-domain.com/',
    DEV: 'https://dev.your-domain.com/',
    TEST: 'https://test.your-domain.com/'
  },
  CloudflareVerify_SITE_KEY: {
    PRE: 'your-site-key',
    DEV: 'your-dev-site-key',
    TEST: 'your-test-site-key'
  },
  debug_mode_main: 'PRE'
}

const onSuccess = (data) => {
  console.log('验证成功:', data)
}

const onCancel = () => {
  console.log('验证取消')
}

// 显示手机号验证
const showPhoneVerification = () => {
  phoneVerificationRef.value?.show()
}

// 获取 Cloudflare 验证 token
const getCloudflareToken = async () => {
  const result = await cloudflareRef.value?.getVerifyToken(CloudFlareScene.LOGIN_SUBMIT)
  console.log('Cloudflare 验证结果:', result)
}
</script>
```

### 3. 验证类型

```typescript
enum VerifyType {
  SetPhoneNumber = 0,      // 设置手机号
  ForgetPassword = 1,      // 忘记密码
  ChangePhoneNumber = 2,   // 修改手机号
  AddWithdrawAccount = 3,  // 添加提现账号
  ChangeWithdrawAccount = 4 // 修改提现账号
}
```

### 4. Cloudflare 验证场景

```typescript
enum CloudFlareScene {
  LOGIN_SUBMIT = 'SCENE_LOGIN',
  LOGIN_PHONE_GET_CODE = 'SCENE_GET_CODE',
  BIND_PHONE_SUBMIT = 'SCENE_BIND_PT_PHONE',
  MODIFY_PHONE_SUBMIT = 'SCENE_CHANGE_PT_PHONE',
  // ... 更多场景
}
```

## API 接口

### PhoneNumberVerification 组件

#### Props
- `verifyType: VerifyType` - 验证类型
- `initialPhone?: string` - 初始手机号
- `onSuccess?: (data: any) => void` - 成功回调
- `onCancel?: () => void` - 取消回调

#### Methods
- `show()` - 显示验证对话框
- `close()` - 关闭验证对话框

### CloudflareController 组件

#### Props
- `config?: AppConfig` - 应用配置

#### Methods
- `getVerifyToken(scene: CloudFlareScene): Promise<VerifyResult>` - 获取验证 token
- `consumeToken()` - 消费 token

## 样式定制

组件使用 scoped CSS，支持以下定制：

### 主题色彩
```css
:root {
  --primary-color: #007bff;
  --error-color: #dc3545;
  --success-color: #28a745;
  --background-color: #ffffff;
}
```

### 响应式断点
- 移动端：`max-width: 480px`
- 平板：`max-width: 768px`
- 桌面：`min-width: 769px`

### 暗色模式
组件自动支持系统暗色模式，通过 `prefers-color-scheme: dark` 媒体查询实现。

## 国际化支持

组件使用 vue-i18n 进行国际化，需要提供以下翻译键：

```json
{
  "security_verification": "安全验证",
  "phone_number": "手机号",
  "verification_code": "验证码",
  "enter_phone_number": "请输入手机号",
  "enter_6_digit_code": "请输入6位验证码",
  "next": "下一步",
  "done": "完成",
  "resend_code": "重新发送",
  "sending": "发送中...",
  "submitting": "提交中...",
  "resend_in": "重新发送倒计时",
  "verification_code_sent_to": "验证码已发送至",
  "phone_number_cannot_be_empty": "手机号不能为空",
  "wrong_phone_number": "手机号格式错误",
  "code_error_please_try_again": "验证码错误，请重试",
  "verification_failed": "验证失败",
  "verification_successful": "验证成功",
  "verification_code_sent_successfully": "验证码发送成功",
  "send_code_failed": "发送验证码失败",
  "verification_error_please_try_again": "验证错误，请重试"
}
```

## 状态管理

组件需要全局状态管理支持，需要实现 `GlobalStore` 接口：

```typescript
interface GlobalStore {
  userdata: UserData | null
  token: string
  loginVerifyType: number
  showTip(message: string): void
  sendSms(params: PhoneVerificationParams): Promise<ApiResponse>
  verifySmsCode(params: SmsVerifyParams): Promise<ApiResponse>
  bindPhone(params: BindPhoneParams): Promise<ApiResponse>
  changePhone(params: ChangePhoneParams): Promise<ApiResponse>
  getStorageData(key: string, defaultValue?: any): any
  setStorageData(key: string, value: any): void
  needScreenUp(): boolean
  getCloudflareController(): CloudflareController
}
```

## 注意事项

1. **手机号格式**：默认支持菲律宾手机号格式 `09xxxxxxxxx` 或 `+639xxxxxxxxx`
2. **验证码长度**：固定为6位数字
3. **倒计时时间**：默认60秒，可通过配置修改
4. **操作锁定**：防止重复操作，默认锁定3秒
5. **移动端适配**：自动处理虚拟键盘弹出时的界面调整

## 浏览器兼容性

- Chrome 60+
- Firefox 60+
- Safari 12+
- Edge 79+

## 许可证

MIT License
