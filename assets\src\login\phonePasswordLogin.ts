import CloudflareCtrl, { CloudFlareScene } from "../Cloudflare/CloudflareCtrl";
import <PERSON><PERSON>om<PERSON> from "../component/UICommon";
import { ALL_APP_SOURCE_CONFIG } from "../Config";
import GameControl from "../GameControl";
import { GEETEST_TYPE, GeetestMgr } from "../geetest/GeetestMgr";
import { DEEP_INDEXZ, E_CHANEL_TYPE, E_GAME_TYPE, E_PAGE_TYPE, EMIT_PARAMS, LOCK_ACCOUNT, MAINTENANCETIPCODE, OPEN_BROADCAST_TYPE, UI_PATH_DIC } from "../GlobalConstant";
import Global from "../GlobalScript";
import Hall from "../hall/Hall";
import { KycMgr } from "../KYC/KycMgr";
import { Md5 } from "../libs/Md5";
import { showMaintenancetip } from "../Maintenancetip";
import { AutoPopManager } from "../mgr/AutoPopManager";
import { serviceMgr } from "../mgr/serviceMgr";
import { uiManager } from "../mgr/UIManager";
import { showMoreGame } from "../MoreGame";
import { showMoreGameList } from "../MoreGameList";
import MoreGameManager from "../MoreGameManager";
import HttpProxy from "../net/HttpProxy";
import HttpUtils from "../net/HttpUtils";
import { GlobalEnum } from "../room/GlobalEnum";
import { showPhoneNumber, PN_VERIFY_TYPE } from "../SetPhoneNumber";
import utils from "../utils/utils";
import ZendeskManager from "../ZendeskManager";
import CaptchaManager from "./CaptchaManager";
import { LoginMgr } from "./LoginMgr";

const { ccclass, property } = cc._decorator;

export enum LOGIN_WAY {
    PhoneCode,
    Password
}

export enum ELOGIN_TYPE {
    LOGIN_PHONE,
    LOGIN_MAYA,
    LOGIN_GOOGLE,
    LOGIN_FACEBOOK
}

export function showPhonePasswordLogin(way: number) {
    uiManager.instance.showDialog(UI_PATH_DIC.phonePasswordLogin, [{ way: way }], null, DEEP_INDEXZ.LOGIN)
}

@ccclass
export default class phonePasswordLogin extends UICommon {
    @property(cc.Node)
    uiBox: cc.Node = null;

    @property(cc.EditBox)
    account_edit: cc.EditBox = null;

    @property(cc.EditBox)
    password_edit: cc.EditBox = null;

    @property(cc.EditBox)
    phone_edit: cc.EditBox = null

    @property(cc.EditBox)
    phone_code: cc.EditBox = null

    @property([cc.Node])
    ndToggleArr: cc.Node[] = []

    @property([cc.Node])
    ndLoginTabArr: cc.Node[] = []

    @property([cc.SpriteFrame])
    frame: Array<cc.SpriteFrame> = [];

    @property(cc.Sprite)
    passwordIsShow: cc.Sprite = null;

    @property(cc.Node)
    ndGetBtn: cc.Node = null

    @property(cc.Node)
    djsBtn: cc.Node = null

    @property(cc.Label)
    lbLoginTips: cc.Label = null

    @property(cc.Label)
    lbCodeLoginTips: cc.Label = null

    @property(cc.Label)
    lbTime: cc.Label = null

    @property(cc.Node)
    verificationCode: cc.Node = null;

    @property(cc.Label)
    verificationPhone: cc.Label = null;

    @property(cc.Node)
    verifiCodeBack: cc.Node = null;

    //关闭 按钮 node  原生要隐藏
    @property(cc.Node)
    close_btn_node: cc.Node = null;

    @property(cc.Node)
    otherLoginWay: cc.Node = null;

    @property(cc.Node)
    privacyNode: cc.Node = null;

    @property(cc.Toggle)
    toggleAgree: cc.Toggle = null;

    //版本号 label
    @property(cc.Label)
    version_label: cc.Label = null

    account: string = "";
    password: string = "";
    m_t: number = 60;
    curLoginWay = LOGIN_WAY.PhoneCode
    m_phone: string = ""
    m_code: string = "";
    _loginType: any = null;
    //登录成功 和失败的回调
    loginOkBackFunc: any = null;
    loginCancelBackFunc: any = null;

    geetest_data: any = {}//极验 的数据  登陆的时候用到
    openType: any = null;//标志用户未登录状态 是否停留在活动页面

    is_login_ing: boolean = false;//防止多次点击登录


    onLoad() {
        cc.director.on('LoginCoseDialog', this.close, this);
        cc.director.on('showLoginErrMsg', this.showLoginErrMsg, this);
        cc.director.on("setLoginPwd_suc", this.onSetLoginPwdSuc, this)
        cc.director.on("captcha_succeed", this.onCaptchaSucceed, this);
        cc.director.on("AutoLoginEvent", this.onEventMaintenanceTry, this);
        cc.director.on(EMIT_PARAMS.GOOGLE_SIGN_IN_SUCCESS, this.google_signin_success, this);
        cc.director.on(EMIT_PARAMS.FACEBOOK_SIGN_IN_SUCCESS, this.facebook_signin_success, this);
        //检测倒计时是否结束
        this.checkCountdown();
        if (cc.sys.isBrowser) {
            let canvas = document.getElementById('GameCanvas');
            canvas.addEventListener("webglcontextlost", function (event) {
                event.preventDefault();
                console.log("WebGL context lost?");
                setTimeout(() => {
                    location.reload(); // 刷新页面
                }, 100);
            });
        }

        Global.getInstance().setPageId(E_PAGE_TYPE.OTHER);
        let btn_spin = Global.getInstance().popNode.getChildByName("btn_spin");
        if (btn_spin) btn_spin.zIndex = -1;
        let btn_envelope = Global.getInstance().popNode.getChildByName("btn_envelope");
        if (btn_envelope) btn_envelope.zIndex = -1;
    }
    protected onEnable(): void {
        Global.getInstance().setPageId(E_PAGE_TYPE.OTHER);
    }
    protected onDestroy(): void {
        cc.director.off('LoginCoseDialog', this.close, this);
        cc.director.off('showLoginErrMsg', this.showLoginErrMsg, this);
        cc.director.off("setLoginPwd_suc", this.onSetLoginPwdSuc, this)
        cc.director.off("captcha_succeed", this.onCaptchaSucceed, this);
        cc.director.off(EMIT_PARAMS.GOOGLE_SIGN_IN_SUCCESS, this.google_signin_success, this);
        cc.director.off(EMIT_PARAMS.FACEBOOK_SIGN_IN_SUCCESS, this.facebook_signin_success, this);
        cc.director.emit("DestroyQueuePopup");
        cc.director.off("AutoLoginEvent", this.onEventMaintenanceTry, this)
        if (window["isShowLoginPage"] == 1) {
            window["isShowLoginPage"] = 0;
        }
    }

    start() {
        if (Global.getInstance().isNative) {
            this.close_btn_node.active = false;
        }
        this.version_label.string = 'Version No. ' + ALL_APP_SOURCE_CONFIG.app_version;
        if (Global.getInstance().getLoginPasswordStatus()) {//login_password:1关 0开
            this.ndToggleArr[1].active = true;
            this.ndToggleArr[1].parent.height = 60;
        } else {
            this.ndToggleArr[1].active = false;
            this.ndToggleArr[1].parent.height = 0;
        }
        //根据后台配置 是否显示 fb google 登录
        let show_fb = Global.getInstance().getLoginFBStatus()
        let show_google = Global.getInstance().getLoginGoogleStatus()
        this.otherLoginWay.active = true;
        if (show_fb && show_google) {

        } else if (show_fb) {
            this.otherLoginWay.getChildByName('btn_facebook').setPosition(cc.v2(0, -50))
            this.otherLoginWay.getChildByName('btn_google').active = false;
        } else if (show_google) {
            this.otherLoginWay.getChildByName('btn_google').setPosition(cc.v2(0, -50))
            this.otherLoginWay.getChildByName('btn_facebook').active = false;
        } else {
            this.otherLoginWay.active = false;
        }
        //这个是一个临时的做法，因为maya先上 web先把登录干掉 [modify by KID]
        let debug_web = this.getBrowserValue("debug_web_login");
        let isPre = ALL_APP_SOURCE_CONFIG.app_hosts.DEBUG_MODE_PRE_PZV != ALL_APP_SOURCE_CONFIG.app_hosts.DEBUG_MODE_RELEASE_PZV
        if (Global.instance.is_mini_game()) {
            //web端 未登录 直接打开
            // if(debug_web == "1" && isPre){
            //     return;
            // }
            // let nologinNode = this.uiBox.getChildByName("nologinNode");
            // this.ndLoginTabArr[0].active = false;
            // this.ndLoginTabArr[1].active = false;
            // this.ndToggleArr[0].active = false;
            // this.ndToggleArr[1].active = false;
            // nologinNode.active = true;
        }
    }

    init(loginWay) {
        this.curLoginWay = loginWay.way
        this.loginCancelBackFunc = loginWay?.loginOk;
        this.loginOkBackFunc = loginWay?.loginError;
        //===openType
        if (loginWay.curPromoPage) this.openType = loginWay.curPromoPage;
        if (loginWay.curPromoDetail) this.openType = "promoDetail";

        let phone = Global.getInstance().getStoreageData(Global.GLOBAL_STORAGE_KEY.PHONE, "");
        let password = Global.getInstance().getStoreageData(Global.GLOBAL_STORAGE_KEY.PASSWORD, "");
        this.account = phone;
        this.password = password;
        this.account_edit.string = phone;
        this.password_edit.string = password;

        this.m_phone = phone
        this.phone_edit.string = phone

        this.show()
        this.resetToggleArr()
        this.ndToggleArr[this.curLoginWay].active = false
        this.ndLoginTabArr[this.curLoginWay].active = true
        if (this.curLoginWay == 0) {
            this.verificationCode.active = false;
            this.verifiCodeBack.active = false;
        }
    }

    onCaptchaSucceed() {
        if (this.curLoginWay == LOGIN_WAY.PhoneCode) {//手机验证码登录
            this._loginType = ELOGIN_TYPE.LOGIN_PHONE;
            this.loginHallServer(0, this.m_phone, null, this.m_code);
        } else {//账号密码登录
            this._loginType == ELOGIN_TYPE.LOGIN_PHONE;
            this.loginHallServer(0, this.account, this.password);
        }
    }

    loginHallServer(code: number, loginparam1: string = null, loginparam2: string = null, loginparam3: string = null, googleToken: string = null) {
        if (code >= 0) {
            let params = {
                "appChannel": ALL_APP_SOURCE_CONFIG.channel,
                "appPackageName": Global.getInstance().getAppBundleId(),
                deviceId: Global.getInstance().getDeviceId(),
                "deviceModel": Global.getInstance().getDeviceName(),
                "deviceVersion": Global.getInstance().getSystemVersion(),
                "appVersion": Global.getInstance().getAppVersion(),
                "sysTimezone": Global.getInstance().getCurrentTimeZone(),
                "sysLanguage": Global.getInstance().getCurrentLanguage(),
                "source": Global.getInstance().getSourceCode(),
                "isNative": cc.sys.isNative ? 1 : 0,
                telephoneCode: "+63",
                registration_channel: ALL_APP_SOURCE_CONFIG.channel
            };

            if (code == 0) {//账号密码登录
                params["login_type"] = "phone";
                params["phone"] = loginparam1;
                if (loginparam2) {
                    params["password"] = Md5.hashStr(loginparam2).toString();
                }
                if (loginparam3) {
                    params["verifyCode"] = loginparam3;
                }
                params["aisec_token"] = Global.getInstance().asCaptchaToken;
            } else if (code == 1) {
                //Maya登录
                params["aisec_token"] = Global.getInstance().asCaptchaToken;
            } else if (code == 3) {
                //Google登录
                params["login_type"] = "google";
                params["googleToken"] = googleToken
                params['google_redirect_uri'] = Global.getInstance().getRedirectUri();
                params["aisec_token"] = Global.getInstance().asCaptchaToken
            } else if (code == 2) {
                //Facebook登录
                params["login_type"] = "facebook";
                params["accessToken"] = loginparam1;
                params["faceUserId"] = loginparam2;
                params["aisec_token"] = Global.getInstance().asCaptchaToken
            }

            if (Global.instance.loginVerifyType == 1) {
                let gee_guard = this.geetest_data?.geetest_guard || '';
                let uInfo = this.geetest_data?.userInfo || '';
                let gee_captcha = this.geetest_data?.geetest_captcha || '';
                params['userInfo'] = uInfo;
                params['geetest_guard'] = gee_guard;
                params['geetest_captcha'] = gee_captcha;
                params['buds'] = this.geetest_data?.buds || '64';
            } else {
                const cfTokenObj = CloudflareCtrl.instance.consumeToken();
                if (cfTokenObj) {
                    params["cf-token"] = cfTokenObj.token;
                    params["cf-scene"] = cfTokenObj.scene;
                }
            }

            this._loginType = code;
            HttpUtils.getInstance().post(3, 3, this, "/common/api/player/login", params, (response) => {
                if (this._loginType == 0) { //账号密码登录
                    if (loginparam1) {
                        Global.getInstance().setStoreageData(Global.GLOBAL_STORAGE_KEY.PHONE, loginparam1);
                    }
                    if (loginparam2) {
                        Global.getInstance().setStoreageData(Global.GLOBAL_STORAGE_KEY.PASSWORD, loginparam2);
                    }
                }
                Global.getInstance().isRegister = response.data.user_info.is_register;
                if (response.data.user_info.is_register) {
                    Global.getInstance().logNewEvent("user_register", { CUID: response.data.user_info.user_id }, 1)
                }
                Global.getInstance().setStoreageData(Global.GLOBAL_STORAGE_KEY.SESSION_ID, response.data.token);

                this.loadToGame(response.data);
                cc.director.emit("LoginCoseDialog")
                // cc.director.emit("checkKycState_login") //登陆后进入游戏 检查kyc信息
                KycMgr.instance.clearData()

                HttpProxy.instance.getDownloadGuideInfo();
                HttpProxy.instance.getRechargeWithdrawConfig();
            }, (response) => {
                if (response && response.code) {
                    //不展示php_code类似文字 使用统一文案 Sorry，Something wrong. Please contact customer service
                    cc.log("response.code ++===>", response.code)
                    if (response.code == MAINTENANCETIPCODE) { //服务器维护
                        showMaintenancetip(response.msg)
                        return;
                    }
                    else if (response.code == 1) {
                        cc.director.emit("showLoginErrMsg", response.msg)
                    } else if (response.code == "103034") {
                        //let errStr = Global.getInstance().getLabel("php_code_" + response.code)
                        let errStr = "Sorry, Something wrong. Please contact customer service";
                        cc.director.emit("showLoginErrMsg", errStr)
                        Global.getInstance().showCommonTip(errStr, this, true);
                    } else if (response.code == "102024" || response.code == "102006") {
                        //let errStr = Global.getInstance().getLabel("php_code_" + response.code)
                        let errStr = "Sorry, Something wrong. Please contact customer service";
                        Global.getInstance().showCommonTip2({ word: errStr, confirm: "Customer service" }, this, false, () => {
                            ZendeskManager.instance().openZenDesk_Ori()
                        });
                    } else if (response.code == "102008" || response.code == 101013 || response.code == 101014) {//LOCK_ACCOUNT
                        this.showLock(response)
                    } else if (response.code == "102042" || response.code == "102045") {
                        Global.getInstance().showCommonTip2({ word: response.msg, confirm: "Customer service" }, this, false, () => {
                            ZendeskManager.instance().openZenDesk_Ori()
                        });
                    } else {
                        //let errStr = Global.getInstance().getLabel("php_code_" + response.code)
                        let errStr = "Sorry, Something wrong. Please contact customer service";

                        //双红框和错误提示
                        let account_red = this.account_edit.node.getChildByName("red");
                        let password_red = this.password_edit.node.getChildByName("red");
                        if (account_red) account_red.opacity = 255;
                        if (password_red) password_red.opacity = 255;

                        let labTips = this.ndLoginTabArr[1].getChildByName("lbTips");
                        if (labTips) {
                            labTips.opacity = 255;
                            let errMsg = "The account does not exist or the password is incorrect";
                            if (response.msg) {
                                errMsg = response.msg;
                            }
                            labTips.getComponent(cc.Label).string = errMsg;
                        }

                        //错误toast
                        if (response.msg) {
                            cc.log("response.code ++44===>", response.msg)
                            Global.getInstance().showSimpleTip(response.msg);
                            cc.director.emit("showLoginErrMsg", response.msg)
                        } else {
                            if (response.msg) {
                                cc.director.emit("showLoginErrMsg", response.msg)
                                Global.getInstance().showSimpleTip(response.msg);
                            } else {
                                cc.director.emit("showLoginErrMsg", errStr)
                                Global.getInstance().showSimpleTip(errStr);
                            }
                        }
                    }
                }
            });
            this.loginOkBackFunc && this.loginOkBackFunc();
        } else {
            let msg = Global.getInstance().getLabel("loginword9");

            if (code == -2) {
                msg = Global.getInstance().getLabel("loginword10");
            }
            cc.director.emit("showLoginErrMsg", msg)
            Global.getInstance().showCommonTip(msg, this, true);
            this.loginCancelBackFunc && this.loginCancelBackFunc();
        }
    }

    showLock(data) {
        uiManager.instance.showDialog(UI_PATH_DIC.LockTip, [data], null, DEEP_INDEXZ.MAX)
    }
    loadToGame(data: any, directGame?: any) {
        // console.log("loadToGame ====>", data)
        Global.getInstance().relogin_clear()
        if (data) {
            Global.getInstance().userdata = data.user_info;
            Global.getInstance().registeraward = data.register_award;
            Global.getInstance().connecturl = data.connection;
            Global.getInstance().token = data.token;
            Global.getInstance().payAccount = data["pay_account"];
            // Global.getInstance().bank = data["bank"];
            Global.getInstance().gameList = data["game_list"];
            Global.getInstance().is_guest = data["is_guest"] && data["is_guest"] == 1;
            Global.getInstance().unreadMarks = data["unread_marks"];

            //核销订单
            if (data["recharge_dot"] && Array.isArray(data["recharge_dot"])) {
                data["recharge_dot"].forEach(element => {
                    Global.getInstance().logNewEvent("purchase", {
                        user_id: Global.getInstance().userdata.user_id,
                        value: parseFloat(element["amount"]),
                        currency: "BRL"
                    }, 2);
                    Global.getInstance().verificationOrder(element["pay_serial_no"]);
                });
            }
            serviceMgr.instance.login();//登陆成功后  客服系统也登陆一下
            Global.getInstance().userdata.withdraw_password = parseInt(data.user_info.withdraw_password);
            Global.getInstance().userdata.login_password = parseInt(data.user_info.login_password);
        }

        //测试修改
        let userId;
        if (data) {
            userId = data.user_info.user_id;
        } else {
            userId = 0;
        }

        // this._loginType 2meta 3google
        let loginType = Global.getInstance().getStoreageData(Global.GLOBAL_STORAGE_KEY.LOGIN_TYPE + userId);
        if (!loginType) {
            Global.getInstance().setStoreageData(Global.GLOBAL_STORAGE_KEY.LOGIN_TYPE + userId, this._loginType + "");
        } else {
            if (parseInt(loginType) != this._loginType) {
                Global.getInstance().setStoreageData(Global.GLOBAL_STORAGE_KEY.LOGIN_TYPE + userId, this._loginType + "");
            }
        }
        Global.getInstance().curLoginType = this._loginType;
        let gameVersion = Global.getInstance().getStoreageData(Global.GLOBAL_STORAGE_KEY.GAMEVERSION, null);
        if (!!gameVersion) {
            Global.getInstance().gameVersion["local"] = JSON.parse(gameVersion);
        }
        //加载完必要的资源后进入大厅
        Global.getInstance().syncLoadRes(() => {
            cc.director.loadScene("gameScene", function () {
                let gameControl = cc.director.getScene().getComponentInChildren(GameControl);
                if (gameControl) {
                    if (directGame && directGame.gameType == E_GAME_TYPE.THIRD_GAME_LIST) {
                        MoreGameManager.instance().isOpeningUI = true
                    }
                    gameControl.preLoadPrefabs(() => {
                        gameControl.loadPrefab("tabbar/tabNode", (node) => {
                            //预先加载tabbar
                            cc.director.emit("DestroyQueuePopup");
                        })
                    });
                }
                if (directGame && directGame.gameType == E_GAME_TYPE.THIRD_GAME) {
                    showMoreGame(directGame.directGameId, directGame.companyId, Number(directGame.is_jump))
                } else if (directGame && directGame.gameType == E_GAME_TYPE.THIRD_GAME_LIST) {
                    showMoreGameList(directGame.directGameId)
                    setTimeout(() => {
                        MoreGameManager.instance().isOpeningUI = false
                    }, 1000)
                }
            });
        });
    }

    resetToggleArr() {
        for (let index = 0; index < this.ndToggleArr.length; index++) {
            const toggle = this.ndToggleArr[index];
            toggle.active = true
            const tab = this.ndLoginTabArr[index];
            tab.active = false
        }
    }
    getCode() {
        if (this.m_phone == "") {
            Global.getInstance().showSimpleTip(Global.getInstance().getLabel("tipword22"));
            return;
        }
        if (Global.getInstance().loginVerifyType === 1) {
            GeetestMgr.instance.geetest_device(GEETEST_TYPE.phone_login_code, (succ) => {
                if (succ) {
                    if (Object.getPrototypeOf(succ) !== Boolean.prototype) {
                        this.geetest_data = succ
                    }
                    this.getCode_true();
                }
            }, this.m_phone);
        } else {
            Global.getInstance().showLoading('getCode');
            CloudflareCtrl.instance.getVerifyToken(CloudFlareScene.LOGIN_PHONE_GET_CODE, res => {
                if (res.code === 0 && res.token) {
                    this.getCode_true();
                }
                Global.getInstance().hideShowLoading('getCode');

            });
        }
    }
    //真正的获取验证码的逻辑 
    getCode_true() {
        let ctype = GlobalEnum.SMS_TYPE.USER_REGISTER;
        let params = {
            phone: this.m_phone,
            appPackageName: Global.getInstance().getAppBundleId(),
            telephoneCode: "+63",
            type: ctype,
            buds: '64',
        }
        if (Global.getInstance().loginVerifyType === 1) {
            let gee_guard = this.geetest_data?.geetest_guard || '';
            let uInfo = this.geetest_data?.userInfo || '';
            let gee_captcha = this.geetest_data?.geetest_captcha || '';
            params["geetest_guard"] = gee_guard;
            params["userInfo"] = uInfo;
            params["geetest_captcha"] = gee_captcha;
            params.buds = this.geetest_data?.buds;
        } else {
            const cfTokenObj = CloudflareCtrl.instance.consumeToken();
            if (cfTokenObj) {
                params["cf-token"] = cfTokenObj.token;
                params["cf-scene"] = cfTokenObj.scene;
            }
        }

        HttpUtils.getInstance().post(3, 3, this, "/common/api/sms/send/short/msg", params, (response) => {
            Global.getInstance().showSimpleTip("Sent successfully")
        }, (response) => {
            if (response && response.code == "103040") {//LOCK_ACCOUNT
                this.showLock(response)
            } else if (response && response?.msg) {
                if (response.code == "102040" || response.code == "102041") {
                    const self = this;
                    setTimeout(() => {
                        self.reSetCode()
                    }, 100);
                }
                this.lbCodeLoginTips.node.active = true;
                this.lbCodeLoginTips.string = response.msg;
            }
            // this.reSetCode();
        });

        this.ndGetBtn.active = false;
        this.djsBtn.active = true;
        let m_t = 60;
        this.lbTime.string = m_t + " s";
        const endTime = Date.now() + m_t * 1000;
        //保存结束时间到本地存储
        Global.getInstance().setStoreageData(Global.GLOBAL_STORAGE_KEY.COUNTDOWN_ENDTIME, endTime);
        this.schedule(this.timer, 1);
    }

    reSetCode() {
        this.unschedule(this.timer);
        this.ndGetBtn.active = true;
        this.djsBtn.active = false;
        const endTime = Date.now();
        //保存结束时间到本地存储
        Global.getInstance().setStoreageData(Global.GLOBAL_STORAGE_KEY.COUNTDOWN_ENDTIME, endTime);
    }

    timer() {
        //开始倒计时
        this.updateCountdown();
    }

    updateCountdown() {
        const eTime = Global.getInstance().getStoreageData(Global.GLOBAL_STORAGE_KEY.COUNTDOWN_ENDTIME, 0);
        const currentTime = Date.now();
        const remainingTime = Math.max(0, eTime - currentTime);
        // 每秒更新一次
        this.lbTime.string = Math.ceil(remainingTime / 1000) + "s";
        if (remainingTime <= 0) {
            this.reSetCode();
        }
    }

    checkCountdown() {
        const endTime = Global.getInstance().getStoreageData(Global.GLOBAL_STORAGE_KEY.COUNTDOWN_ENDTIME, 0);
        const currentTime = Date.now();
        const remainingTime = Math.max(0, endTime - currentTime);
        if (remainingTime > 0) {
            // 倒计时未结束
            this.ndGetBtn.active = false;
            this.djsBtn.active = true;
            this.lbTime.node.parent.active = true;
            this.lbTime.string = Math.ceil(remainingTime / 1000) + "s";
            this.schedule(() => { this.updateCountdown() }, 1);
        } else {
            // 倒计时结束
            this.reSetCode();
        }
    }


    onAccountText(text, editbox, customEventData) {
        this.m_phone = text;//修复bug
        this.account = text;
    }

    onPasswordText(text, editbox, customEventData) {
        this.password = text;
    }


    onCodeAccountText(text, editbox, customEventData) {
        this.m_phone = text;
    }

    onCodetext(text, editbox, customEventData) {
        this.m_code = text;
    }

    close() {
        this.hide()
        cc.director.emit("ReturnToHome2");
        if (!Global.getInstance().token) {
            cc.director.emit("DestroyQueuePopup");
        } else {
            // AutoPopManager.clearQueuePopup();
        }
    }

    onClickToggle(sender, customData) {
        this.resetToggleArr()
        let tag = Number(customData)
        if (this.curLoginWay == tag) return
        this.curLoginWay = tag
        this.ndToggleArr[tag].active = false
        this.ndLoginTabArr[tag].active = true
        if (tag == 1) {
            this.account_edit.string = this.phone_edit.string;
        }
    }
    //点击密码登陆 先geetest验证
    loginClick() {
        let errmsg = ""
        //确保手机号码是一致的
        if (this.m_phone != null) {
            this.account = this.m_phone;
        }
        console.log('-----------this.m_phone:', this.m_phone)
        if (this.account == null || this.account == "" || this.password == null || this.password == "") {
            errmsg = "The account does not exist or the password is incorrect";
            let red = this.account_edit.node.getChildByName("red");
            let ps_red = this.password_edit.node.getChildByName("red");
            let lbtips = this.ndLoginTabArr[1].getChildByName("lbTips");
            let password_lbtips = this.ndLoginTabArr[1].getChildByName("ps_lbTips");
            if (red) red.opacity = 255;
            if (ps_red) ps_red.opacity = 255;
            if (lbtips) {
                lbtips.opacity = 255;
                if (password_lbtips) password_lbtips.opacity = 1;
                lbtips.getComponent(cc.Label).string = errmsg;
            }
            return;
        }

        if (!utils.isPhilippinePhoneNumber(this.account)) {
            errmsg = "Please enter a valid 10-digit mobile phone number."
            let red = this.account_edit.node.getChildByName("red");
            if (red) red.opacity = 255;
            let labTips = this.ndLoginTabArr[1].getChildByName("lbTips");
            if (labTips) {
                labTips.opacity = 255;
                labTips.getComponent(cc.Label).string = errmsg;
            }
            return
        }
        if (this.is_login_ing) return;
        this.is_login_ing = true;
        let self = this;
        //关闭之后 也可再次点击
        // setTimeout(() => {
        //     self.is_login_ing = false;
        // }, 10000);
        if (Global.getInstance().loginVerifyType === 1) {
            GeetestMgr.instance.geetest_device(GEETEST_TYPE.password_login, (succ) => {
                self.is_login_ing = false;
                if (succ) {
                    if (Object.getPrototypeOf(succ) !== Boolean.prototype) {
                        this.geetest_data = succ
                    }
                    this.loginClick_true();
                }
            }, this.account, () => {
                self.is_login_ing = false;
            })
        } else {
            CloudflareCtrl.instance.getVerifyToken(CloudFlareScene.LOGIN_SUBMIT, res => {
                if (res.code === 0 && res.token) {
                    this.loginClick_true();
                }
            });
        }
    }
    /**点击Log in 真实登陆*/
    loginClick_true() {
        //Toggle Agree
        if (this.toggleAgree.isChecked == false) {//未勾选
            uiManager.instance.showDialog(UI_PATH_DIC.PrivacyTip, [this.toggleAgree], null, DEEP_INDEXZ.PRIVACY_TIPS)
        } else {
            this.resetLoginErrMsg()
            CaptchaManager.getCaptcha()
        }
    }

    onClickBackBtn() {
        if (this.verificationCode) this.verificationCode.active = false;
        if (this.verifiCodeBack) this.verifiCodeBack.active = false;
        if (Global.getInstance().isNative) {
            this.close_btn_node.active = false;
        } else {
            //web 显示
            this.close_btn_node.active = true;
        }
        if (this.ndLoginTabArr[0]) this.ndLoginTabArr[0].active = true;
        if (this.ndToggleArr[1]) {//login_password:1关 0开
            if (Global.getInstance().getLoginPasswordStatus()) {//login_password:1关 0开
                this.ndToggleArr[1].active = true;
                this.ndToggleArr[1].parent.height = 60;
            } else {
                this.ndToggleArr[1].active = false;
                this.ndToggleArr[1].parent.height = 0;//去除镂空
            }
        }
        this.resetLoginErrMsg();
        //if (this.otherLoginWay) this.otherLoginWay.active = true;
        if (this.privacyNode) this.privacyNode.active = true;
    }
    /**点击Register/Log in */
    onClickCodeLogin() {
        let errMsg = "";
        this.m_phone = this.phone_edit.string;
        if (this.m_phone == null || this.m_phone == "") {
            errMsg = "Please enter the phone number."
            let red = this.phone_edit.node.getChildByName("red");
            let tips = this.ndLoginTabArr[0].getChildByName("lbTips");
            if (red) red.active = true;
            if (tips) {
                tips.active = true;
                tips.getComponent(cc.Label).string = errMsg;
            }
            return;
        }
        //验证手机号
        if (!utils.isPhilippinePhoneNumber(this.m_phone)) {
            errMsg = "Please enter a valid 10-digit mobile phone number.";
            let phone_red = this.phone_edit.node.getChildByName("red");
            if (phone_red && this.curLoginWay == 0) {
                phone_red.active = true;
                let labtips = this.ndLoginTabArr[0].getChildByName("lbTips");
                labtips.active = true;
                labtips.getComponent(cc.Label).string = errMsg;
                return;
            }
        }

        //Toggle Agree
        if (this.toggleAgree.isChecked == false) {//未勾选
            uiManager.instance.showDialog(UI_PATH_DIC.PrivacyTip, [this.toggleAgree], null, DEEP_INDEXZ.PRIVACY_TIPS)
        } else {
            //隐藏Login with Password
            this.ndToggleArr[1].active = false;
            this.ndLoginTabArr[0].active = false;
            let close = this.uiBox.getChildByName("close");
            if (close) close.active = false;
            this.verificationCode.active = true;
            this.verifiCodeBack.active = true;
            // if (this.otherLoginWay) this.otherLoginWay.active = false;
            if (this.privacyNode) this.privacyNode.active = false;
            if (this.verificationPhone) this.verificationPhone.string = this.formatPhoneNumber(this.m_phone);
        }
    }

    /**点击Terms of use*/
    onClickTerms() {
        uiManager.instance.showDialog(UI_PATH_DIC.PrivacyTip, [null, "term"], null, DEEP_INDEXZ.PRIVACY_TIPS)
    }

    /**点击Privacy Policy*/
    onClickPrivacy() {
        uiManager.instance.showDialog(UI_PATH_DIC.PrivacyTip, [null, "privacy"], null, DEEP_INDEXZ.PRIVACY_TIPS)
    }

    formatPhoneNumber(phoneNumber: string) {
        //保留前两位和后四位，中间用****替换
        return `${phoneNumber.slice(0, 2)}****${phoneNumber.slice(6)}`;
    }

    passwordIsShowClick() {
        if (this.passwordIsShow.spriteFrame == this.frame[1]) {
            this.passwordIsShow.spriteFrame = this.frame[0];
            this.password_edit.inputFlag = cc.EditBox.InputFlag.PASSWORD;
        } else {
            this.passwordIsShow.spriteFrame = this.frame[1];
            this.password_edit.inputFlag = cc.EditBox.InputFlag.DEFAULT;
        }
    }

    onEdittingDidBegin(editbox: cc.EditBox) {
        Global.instance.scrollTo(0, 0, 100);
        Global.getInstance().editParentMove(editbox, this.uiBox, 0)
        editbox.placeholderLabel.node.name = editbox.placeholder
        editbox.placeholder = ""

        //账号、密码红色框
        let red = editbox.node.getChildByName("red");
        let ps_red = this.password_edit.node.getChildByName("red");
        if (red) red.opacity = 1;
        if (ps_red) ps_red.opacity = 1;

        //账号密码 红色提示
        let tips = this.ndLoginTabArr[1].getChildByName("lbTips").getComponent(cc.Label);
        let ps_tips = this.ndLoginTabArr[1].getChildByName("ps_lbTips").getComponent(cc.Label);
        if (tips) tips.string = '';
        if (ps_tips) ps_tips.string = '';

        //验证码登录 提示
        let labtips = this.ndLoginTabArr[0].getChildByName("lbTips");
        if (labtips) labtips.opacity = 1;
        console.log("onEdittingDidBegin ==>", editbox.string)
    }

    onEdittingDidEnd(editbox: cc.EditBox) {
        Global.instance.scrollTo(0, 0, 100);
        if (Global.getInstance().needScreenUp()) {
            this.uiBox.y = 0
        }
        editbox.placeholder = editbox.placeholderLabel.node.name

        let errmsg = ""
        if (this.curLoginWay == LOGIN_WAY.Password) {
            if (editbox.string == null || editbox.string == "" || editbox.string.trim() == "") {
                editbox.string = "";
                errmsg = "Please enter the phone number.";
                let red = this.account_edit.node.getChildByName("red");
                let tips = this.ndLoginTabArr[1].getChildByName("lbTips");
                if (red) red.opacity = 255;
                if (tips) {
                    tips.opacity = 255;
                    tips.getComponent(cc.Label).string = errmsg;
                }
                return;
            }
            if (editbox.string && editbox.string.length > 0) {
                if (!utils.isPhilippinePhoneNumber(editbox.string)) {
                    errmsg = "Please enter a valid 10-digit mobile phone number.";
                    let red = this.account_edit.node.getChildByName("red");
                    let tips = this.ndLoginTabArr[1].getChildByName("lbTips");
                    if (red) red.opacity = 255;
                    if (tips) {
                        tips.opacity = 255;
                        tips.getComponent(cc.Label).string = errmsg;
                    }
                    return;
                }
            }
        }

        if (this.curLoginWay == LOGIN_WAY.PhoneCode) {
            if (editbox.string == null || editbox.string == "" || editbox.string.trim() == "") {
                editbox.string = "";
                errmsg = "Please enter the phone number."
                let red = this.phone_edit.node.getChildByName("red");
                let tips = this.ndLoginTabArr[0].getChildByName("lbTips");
                if (red) red.opacity = 255;
                if (tips) {
                    tips.opacity = 255;
                    tips.getComponent(cc.Label).string = errmsg;
                }
                return;
            }
            if (editbox.string && editbox.string.length > 0) {
                if (!utils.isPhilippinePhoneNumber(editbox.string)) {
                    errmsg = "Please enter a valid 10-digit mobile phone number."
                    let red = this.phone_edit.node.getChildByName("red");
                    let tips = this.ndLoginTabArr[0].getChildByName("lbTips");
                    if (red) red.opacity = 255;
                    if (tips) {
                        tips.opacity = 255;
                        tips.getComponent(cc.Label).string = errmsg;
                    }
                    return
                }
            }
        }
    }

    onEdittingCodeDidBegin(editbox: cc.EditBox) {
        Global.getInstance().editParentMove(editbox, this.uiBox, 300)
        editbox.placeholderLabel.node.name = editbox.placeholder
        editbox.placeholder = ""
        //红框
        let red = editbox.node.getChildByName("red");
        if (red) red.opacity = 1;

        //账号密码 红色提示
        let tips = this.ndLoginTabArr[1].getChildByName("lbTips").getComponent(cc.Label);
        let ps_tips = this.ndLoginTabArr[1].getChildByName("ps_lbTips").getComponent(cc.Label);
        if (tips) tips.string = '';
        if (ps_tips) ps_tips.string = '';

        let account_red = this.account_edit.node.getChildByName("red");
        if (account_red.opacity == 255) {
            let lbtips = this.ndLoginTabArr[1].getChildByName("lbTips");
            if (lbtips) {
                if (this.account.length == 0) {
                    lbtips.opacity = 255;
                    lbtips.getComponent(cc.Label).string = "Please enter the phone number.";
                }
                if (this.account.length > 0) {
                    lbtips.opacity = 255;
                    if (!utils.isPhilippinePhoneNumber(this.account))
                        lbtips.getComponent(cc.Label).string = "Please enter a valid 10-digit mobile phone number.";
                }
            }
        }
    }

    onEdittingCodeDidEnd(editbox: cc.EditBox) {
        if (Global.getInstance().needScreenUp()) {
            this.uiBox.y = 0
        }
        editbox.placeholder = editbox.placeholderLabel.node.name
        //登录密码输入框为空
        let errMsg = "";
        if (editbox.string == null || editbox.string == "" || editbox.string.trim() == "") {
            editbox.string = "";
            let tips = this.ndLoginTabArr[1].getChildByName("ps_lbTips");
            if (tips) {
                tips.active = true;
                errMsg = "Please enter correct Login Password";
                tips.getComponent(cc.Label).string = errMsg;
            }
        }
        Global.getInstance().request_focus_toself();
    }
    clickCoedLogin() {
        let errMsg = ""
        if (this.m_code == "") {
            errMsg = Global.getInstance().getLabel("tipword26")
            this.showLoginErrMsg(errMsg)
            return;
        }
        if (this.m_code.length < 6) {
            this.showLoginErrMsg("Incorrect verification code")
            return;
        }
        if (this.is_login_ing) return;
        this.is_login_ing = true;
        let self = this;
        //关闭之后 也可再次点击
        // setTimeout(() => {
        //     self.is_login_ing = false;
        // }, 10000);
        if (Global.getInstance().loginVerifyType === 1) {
            GeetestMgr.instance.geetest_device(GEETEST_TYPE.phone_code_login, (succ) => {
                self.is_login_ing = false;
                if (succ) {
                    if (Object.getPrototypeOf(succ) !== Boolean.prototype) {
                        this.geetest_data = succ
                    }
                    this.clickCoedLogin_true();
                }
            }, this.m_phone, () => {
                self.is_login_ing = false;
            });
        } else {
            CloudflareCtrl.instance.getVerifyToken(CloudFlareScene.LOGIN_SUBMIT, res => {
                if (res.code === 0 && res.token) {
                    // 走登录流程
                    this.clickCoedLogin_true();
                }
            });
        }

    }
    /**点击获取Code页面的Log in */
    clickCoedLogin_true() {
        CaptchaManager.getCaptcha()
        return;
    }

    /**点击forgot按钮 */
    forgotClick() {
        let isClick = false;
        let errMsg = ""
        if (this.account == "") {
            errMsg = Global.getInstance().getLabel("tipword22")
            this.showLoginErrMsg(errMsg, 2)
            return;
        }

        //判定手机号
        if (!utils.isPhilippinePhoneNumber(this.account)) {
            errMsg = "Please enter a valid 10-digit mobile phone number."
            this.showLoginErrMsg(errMsg, 2)
            return
        }

        //用户点击forget需要检测当前账号是否注册
        HttpUtils.getInstance().post(3, 3, this, "/common/api/get/phone/exists", {
            phone: this.account,
        }, (response) => {
            if (response.data) {//账号已注册
                if (response.data.exists == 1) {
                    this.resetLoginErrMsg()
                    showPhoneNumber(PN_VERIFY_TYPE.ForgetPassword, this.account);
                } else if (response.data.exists == 0) {
                    let strTip = "Please register an account first";
                    this.showLoginErrMsg(strTip, 2);
                } else if (response.data.exists == 2) {
                    this.showLock(response)
                }
            }
        }, (response) => {
            //请求失败了
        });
    }

    showLoginErrMsg(errMsg, type?: number) {
        if (this.curLoginWay == LOGIN_WAY.Password) {
            this.lbLoginTips.node.opacity = 255;
            this.lbLoginTips.string = errMsg;
            if (type && type == 2) {
                let red = this.account_edit.node.getChildByName("red")
                if (red) red.opacity = 255;
            } else {
                let red = this.password_edit.node.getChildByName("red")
                if (red) red.opacity = 255;
            }
        } else {
            this.lbCodeLoginTips.node.opacity = 255;
            this.lbCodeLoginTips.string = errMsg;
            if (type && type == 2) {
                let red = this.phone_edit.node.getChildByName("red")
                if (red) red.opacity = 255;
            } else {
                let red = this.phone_code.node.getChildByName("red")
                if (red) red.opacity = 255;
            }
        }
    }

    resetLoginErrMsg() {
        if (this.curLoginWay == LOGIN_WAY.Password) {
            this.lbLoginTips.string = ""
        } else {
            this.lbCodeLoginTips.string = "";
            let codeRed = this.phone_code.node.getChildByName("red");
            if (codeRed) codeRed.opacity = 1;
        }
    }

    onSetLoginPwdSuc(data: { phone: string, password: string }) {
        if (this.curLoginWay == LOGIN_WAY.PhoneCode) {
            return
        }
        this.account_edit.string = data.phone;
        let red_account = this.account_edit.node.getChildByName("red")
        if (red_account) red_account.opacity = 1;
        this.password_edit.string = data.password;
        let red = this.password_edit.node.getChildByName("red")
        if (red) red.opacity = 1;
        this.m_phone = data.phone
        this.password = data.password
    }

    onEventMaintenanceTry() {
        if (this.curLoginWay == LOGIN_WAY.PhoneCode) {
            this.onClickCodeLogin()
        } else {
            this.loginClick()
        }
    }

    /**点击agreed勾选框 */
    onClickToggleAgree() {
        console.log("onClickToggleAgree!!!");
    }

    /**点击pagcor */
    onClickPagcorBtn() {
        setTimeout(() => {
            let url = "https://www.pagcor.ph/regulatory/responsible-gaming.php";
            utils.openUrl(url);
        })
    }

    getBrowserValue(value) {
        var query = window.location.search.substring(1);
        var vars = query.split("&");
        for (var i = 0; i < vars.length; i++) {
            var pair = vars[i].split("=");
            if (pair[0] == value) {
                return pair[1];
            }
        }
        return null;

    }

    //Facebook 登录
    tap_facebook_login() {
        LoginMgr.instance.facebook_login();
    }

    //Google登录
    tap_google_login() {
        LoginMgr.instance.google_login();
    }

    //google 登录成功 通知
    google_signin_success(idToken?) {
        this.loginHallServer(3, null, null, null, idToken);
    }

    //facebook 登录成功通知
    facebook_signin_success(accessToken?, userID?) {
        this.loginHallServer(2, accessToken, userID);
    }
}
