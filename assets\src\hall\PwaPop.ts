import { UIComponent } from "../customComponent/UIComponent";
import { DEEP_INDEXZ, UI_PATH_DIC } from "../GlobalConstant";
import Global from "../GlobalScript";
import utils from "../utils/utils";

const { ccclass, property } = cc._decorator;

export function showPwaPop() {
    // 显示 loading
    Global.getInstance().showLoading('PwaPop');

    // 加载弹窗
    cc.resources.load(UI_PATH_DIC.PwaPop, (err: Error, prefab: cc.Prefab) => {
        // 隐藏 loading
        Global.getInstance().hideShowLoading('PwaPop');

        if (!prefab) {
            // 预制体加载失败，飘字提醒
            Global.getInstance().showSimpleTip(Global.getInstance().getLabel("tipword101"));
            return;
        }

        let parent = Global.getInstance().popNode
        if (!parent) {
            // 弹窗父节点不存在
            return;
        }

        if (parent.getChildByName('PwaPop')) {
            // 弹窗已存在
            return;
        }

        // 创建并显示弹窗
        const node = cc.instantiate(prefab);
        node.name = 'PwaPop';
        parent.addChild(node);
        node.zIndex = DEEP_INDEXZ.COMMON_TIPS;
    });
}

@ccclass
export default class PwaPop extends UIComponent {
    onLoad(): void {
        super.onLoad();

        this.initUI();

        utils.addTodayJumpTimes(Global.GLOBAL_STORAGE_KEY.PWA_TIMES);
    }

    onDestroy(): void {
        super.onDestroy();

        cc.director.emit("DestroyQueuePopup");
    }

    initUI() {
        // const canPwaInstall = window["canPwaInstall"] && window["canPwaInstall"]();
        // this.nodeAtNode("nd_pwa").active = canPwaInstall;
        // this.nodeAtNode("nd_guide").active = !canPwaInstall;
        this.nodeAtNode("nd_pwa").active = cc.sys.os != cc.sys.OS_IOS;
        this.nodeAtNode("nd_guide").active = cc.sys.os == cc.sys.OS_IOS;

        const value = this.gameData.getPwaConfig();
        if (value) {
            const switchTab = value.desktop_shortcut_key;
            const info = value.info;

            this.nodeAtSpriteSwitcher("bg").selectedIndex = switchTab;
            this.setLabel("txt_content", info);

            this.nodeAtNode("btn_install_app").active = switchTab == 1;
            this.nodeAtNode("btn_install_web").active = switchTab == 1;
            this.nodeAtNode("btn_install_web2").active = switchTab != 1;
        }
    }

    onClick(name: string, btn: cc.Node): void {
        if (name.startsWith("btn_close")) {
            this.hide();
        }
        else if (name.startsWith("btn_install_web")) {
            if (window["pwaInstall"]) {
                window["pwaInstall"]();
                this.hide();
            }
        }
        else if (name == "btn_install_app") {
            const downloadUrl = this.gameData.forceConfig.download_url;
            utils.openUrl(downloadUrl, true);
        }
    }
}
