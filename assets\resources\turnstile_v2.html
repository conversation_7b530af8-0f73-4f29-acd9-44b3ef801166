<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <script src="https://challenges.cloudflare.com/turnstile/v0/api.js" async defer></script>
    <style>
        body { 
            margin: 0; 
            display: flex; 
            justify-content: center; 
            align-items: center; 
            width: 100vw;
            height: 100vh; 
            background: transparent; /* 透明背景 */ 
        }
        .cf-turnstile_other {
            transform: scale(3);
        }
        .cf-turnstile_android {
            transform: scale(0.9);
        }
    </style>
</head>
<body>
    <div class="cf-turnstile" 
        data-theme="light" 
        data-language="en" 
        data-callback="onTurnstileSuccess" 
        data-error-callback="onTurnstileError" 
        data-timeout-callback="onTurnstileTimeout" 
        data-unsupported-callback="onTurnstileUnsupported" 
        data-retry="never"
    >
    </div>
    <script>
        function initTurnstile() {
            // 解析 url 参数
            const urlParams = new URLSearchParams(window.location.search);
            const siteKey = urlParams.get('siteKey') || '0x4AAAAAABlWvj83_AdIyJ04';
            const isNative = urlParams.get('isNative') == 1 ? 1 : 0;
            const os = urlParams.get('os') || 'web';
            const appearance = urlParams.get('appearance');

            window.PLATFORM_INFO = { isNative, os };

            const turnstileDiv = document.querySelector('.cf-turnstile');
            if (turnstileDiv) {
                // 设置 siteKey
                turnstileDiv.setAttribute('data-sitekey', siteKey);
            }

            if (appearance) {
                // 设置 appearance
                turnstileDiv.setAttribute('data-appearance', appearance);
            }

            if (isNative == 1 && os === 'android') {
                turnstileDiv.classList.add('cf-turnstile_android');
            } else {
                turnstileDiv.classList.add('cf-turnstile_other');
            }
        }

        // 页面加载完成后初始化
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initTurnstile);
        } else {
            initTurnstile();
        }

        // 检测是否在原生平台
        function isNativePlatform() {
            if (window.PLATFORM_INFO && window.PLATFORM_INFO.isNative == 1) {
                return true;
            }
            return false;
        }

        // 发送消息到原生平台
        function sendToNative(action, data) {
            if (isNativePlatform()) {
                const params = new URLSearchParams();
                params.append('action', action);
                if (data) {
                    Object.keys(data).forEach(key => {
                        params.append(key, data[key]);
                    });
                }
                const url = 'turnstile://callback?' + params.toString();
                // window.location.href = url;

                // 用 iframe 跳转代替 window.location.href，兼容性更好
                const iframe = document.createElement('iframe');
                iframe.style.display = 'none';
                iframe.src = url;
                document.body.appendChild(iframe);

                // 可选：短时间后移除 iframe
                setTimeout(() => {
                    document.body.removeChild(iframe);
                }, 200);
            }
        }

        // 验证成功
        function onTurnstileSuccess(token) {
            if (isNativePlatform()) {
                // 原生平台
                sendToNative('success', { token: token });
            } else {
                // Web 平台
                if (window.parent !== window) {
                    window.parent.postMessage({ type: 'onTurnstileSuccess', token }, '*');
                }
            }
        }

        // 验证失败
        function onTurnstileError(errCode) {
            if (isNativePlatform()) {
                // 原生平台
                sendToNative('error', { errCode: errCode });
            } else {
                // Web 平台
                window.parent.postMessage({ type: 'onTurnstileError', errCode }, '*');
            }
        }

        // 验证超时
        function onTurnstileTimeout() {
            if (isNativePlatform()) {
                // 原生平台
                sendToNative('timeout');
            } else {
                // Web 平台
                window.parent.postMessage({ type: 'onTurnstileTimeout' }, '*');
            }
        }

        // 不支持
        function onTurnstileUnsupported() {
            if (isNativePlatform()) {
                // 原生平台
                sendToNative('unsupported');
            } else {
                // Web 平台
                window.parent.postMessage({ type: 'onTurnstileUnsupported' }, '*');
            }
        }
    </script>
</body>
</html>