// 注册后（注册奖励，签到，确认邀请人。按照这个优先级弹）
// 登录后（签到，升级奖励。按照这个优先级弹）
//LuckyUser RewardUI 

import { ALL_APP_SOURCE_CONFIG } from "../Config";
import { showFullScreenTip } from "../FullScreenTip";
import { E_CHANEL_TYPE, E_PAGE_TYPE } from "../GlobalConstant";
import Global from "../GlobalScript";
import { INGSME_TYPE, KycMgr } from "../KYC/KycMgr";
import MoreGameManager from "../MoreGameManager";
import { showPopupBanner } from "../PopupBanners";
import { showVIPTip } from "../VIP/VipTip";
import { showWalletTaskPop } from "../Wallet/WalletTask";
import { showActivityBonus } from "../hall/ActivityBonus";
import { showEnvelopePop } from "../hall/Envelope";
import { showForceJumpPop } from "../hall/ForceJump";
import { showRankPop } from "../hall/LeaderBoardPop";
import { showRankPopJili } from "../hall/LeaderBoardPopJili";
import { showPwaPop } from "../hall/PwaPop";
import { showSpinPop } from "../hall/SpinWheel";
import { showTaskPop } from "../hall/Task";
import { showTip21Old } from "../hall/Tips21Old";
import { LOGIN_WAY, showPhonePasswordLogin } from "../login/phonePasswordLogin";

export const enum EnumPopUpPrefabURL {
    PhonePasswordLogin,
    Tip21Old,
    FullScreenTip,
    Envelope,//首存红包奖励弹窗
    VipTip,//升级VIP提示
    PopupBanners,
    RegisterBonus,//注册/绑定奖励
    ActivityBonus,//所有活动奖励 反水之类的 必须领取才会获得 lemoon
    KYCPop,//kyc弹窗 lemoon 加入自动弹窗
    Task,//任务弹窗 加入自动弹窗
    LeaderBoardPop,//rank
    LeaderBoardPopJILI,//rank
    SpinWheel,//转盘
    WalletTask,//奖金钱包
    ForceJump,//强制引导
    PwaPop,//PWA
}

//priority越大，越先弹出
const HallAutoPopConfig = {
    [EnumPopUpPrefabURL.PwaPop]: { priority: 19 },
    [EnumPopUpPrefabURL.ForceJump]: { priority: 18 },
    [EnumPopUpPrefabURL.PhonePasswordLogin]: { priority: 17 },
    [EnumPopUpPrefabURL.KYCPop]: { priority: 16 },
    [EnumPopUpPrefabURL.Tip21Old]: { priority: 15 },
    [EnumPopUpPrefabURL.WalletTask]: { priority: 14 },
    [EnumPopUpPrefabURL.Envelope]: { priority: 13 },
    [EnumPopUpPrefabURL.VipTip]: { priority: 12 },
    [EnumPopUpPrefabURL.ActivityBonus]: { priority: 11 },
    [EnumPopUpPrefabURL.PopupBanners]: { priority: 10 },
    [EnumPopUpPrefabURL.LeaderBoardPop]: { priority: 8 },
    [EnumPopUpPrefabURL.LeaderBoardPopJILI]: { priority: 7 },
    [EnumPopUpPrefabURL.SpinWheel]: { priority: 6 },
    [EnumPopUpPrefabURL.Task]: { priority: 5 },
    [EnumPopUpPrefabURL.RegisterBonus]: { priority: 4 },
}

export interface AutoPopInfo {
    popType: number,
}

export const AutoPopMgr = (function () {
    let is_showing = false;
    function checkNextPopDialog(popName) {
        let bPop = false;
        let realPopName = Number(popName);
        switch (realPopName) {
            case EnumPopUpPrefabURL.PhonePasswordLogin:
                if (Global.instance.is_mini_game()) {
                    bPop = false;
                } else {
                    bPop = window["isShowLoginPage"] == 1;
                }
                break;
            case EnumPopUpPrefabURL.KYCPop:
                bPop = KycMgr.instance.needShowPopKYC()
                break;
            case EnumPopUpPrefabURL.Tip21Old:
                bPop = Global.getInstance().token ? false : true;
                if (window['showTip21Old']) bPop = false;
                break
            case EnumPopUpPrefabURL.FullScreenTip:
                bPop = MoreGameManager.instance().isNeedShowFullScreenTip()
                break
            case EnumPopUpPrefabURL.Envelope:
                bPop = Global.getInstance().isNeedShowEnvelopePop();
                if (window['EnvelopePop']) bPop = false;
                break;
            case EnumPopUpPrefabURL.VipTip:
                bPop = Global.getInstance().isNeedShowVipTip();
                break;
            case EnumPopUpPrefabURL.ActivityBonus:
                bPop = Global.getInstance().isNeedShowActivityBonus()
                break;
            case EnumPopUpPrefabURL.PopupBanners:
                bPop = Global.getInstance().isNeedShowPopupBanner()
                break;
            case EnumPopUpPrefabURL.RegisterBonus:
                bPop = Global.getInstance().isNeedShowRegisterBonus();
                break;
            case EnumPopUpPrefabURL.Task:
                bPop = Global.getInstance().isNeedShowTaskPop();
                break;
            case EnumPopUpPrefabURL.LeaderBoardPop:
                bPop = Global.getInstance().isNeedShowRankPop();
                if (window['LeaderBoardPop']) bPop = false;
                break;
            case EnumPopUpPrefabURL.LeaderBoardPopJILI:
                bPop = Global.getInstance().isNeedShowRankPopJili();
                if (window['LeaderBoardPopJILI']) bPop = false;
                break;
            case EnumPopUpPrefabURL.SpinWheel:
                bPop = Global.getInstance().isNeedShowSpin();
                if (window['SpinWheel']) bPop = false;
                break;
            case EnumPopUpPrefabURL.WalletTask:
                bPop = Global.getInstance().isNeedShowWalletTask();
                if (window['WalletTask']) bPop = false;
                break;
            case EnumPopUpPrefabURL.ForceJump:
                bPop = Global.getInstance().isNeedForceJump();
                break;
            case EnumPopUpPrefabURL.PwaPop:
                bPop = Global.getInstance().isNeedPwaPop();
                if (window['PwaPop']) bPop = false;
                break;
        }
        return { bPop, realPopName };
    }
    return {
        autoPopupDialog(bForce: boolean = false) {
            if (this.is_showing) return;
            //处理自动弹窗必须在hall页弹出
            let ishall = Global.getInstance().getPageId() == E_PAGE_TYPE.HALL;
            if (!ishall && !bForce) {
                return;
            }

            //增加排序
            //每次找到最大的
            let result_true = null;
            let max_priority = 0;
            for (let type in HallAutoPopConfig) {
                let key = HallAutoPopConfig[type]
                let result = checkNextPopDialog(type);
                if (result.bPop) {
                    if (key.priority > max_priority) {
                        //找到最高级的 先弹出
                        result_true = result
                        max_priority = key.priority// 修复bug 排序没有生效
                    }
                }
            }
            if (result_true) {
                // console.log('------弹出框 是什么？',result_true.realPopName)
                this.showQueuePopup(result_true.realPopName)
            } else {
                //防止 重新登录 不弹出 遗留bug jhon 测试出来 
                this.is_showing = false;
            }
        },
        //判断还有没有弹窗
        has_pops() {
            if (this.is_showing) return true;
            let result_true = false;
            let max_priority = 0;
            for (let type in HallAutoPopConfig) {
                let key = HallAutoPopConfig[type]
                let result = checkNextPopDialog(type);
                if (result.bPop) {
                    if (key.priority > max_priority) {
                        //找到最高级的 先弹出
                        result_true = true
                        max_priority = key.priority// 修复bug 排序没有生效
                    }
                }
            }
            return result_true;
        },
        // 显示弹框
        showQueuePopup: function (curPopType) {
            switch (curPopType) {
                case EnumPopUpPrefabURL.PhonePasswordLogin:
                    this.is_showing = true;
                    showPhonePasswordLogin(LOGIN_WAY.PhoneCode);
                    break
                case EnumPopUpPrefabURL.KYCPop:
                    this.is_showing = true;
                    let self = this;
                    //验证一下kyc 如果有 自动弹出
                    KycMgr.instance.verify_kyc(INGSME_TYPE.Login, (isVerity) => {
                        if (isVerity) {
                            this.destroyQueuePopup()//直接向下执行
                        } else {
                            setTimeout(() => {
                                if (!KycMgr.instance.poping) {
                                    self.destroyQueuePopup()//直接向下执行
                                }
                            }, 100);
                        }
                        //这里是 认证过之后的 逻辑 其他逻辑会自动 执行 是非对错都要调用
                    });
                    break
                case EnumPopUpPrefabURL.Tip21Old:
                    if (!Global.getInstance().token && !window['showTip21Old']) {
                        this.is_showing = true;
                        window['showTip21Old'] = true
                        showTip21Old();
                    }
                    break
                case EnumPopUpPrefabURL.FullScreenTip:
                    //to do先做线上的需求
                    if (MoreGameManager.instance().isBrowserAndroidDevice()) {
                        this.is_showing = true;
                        showFullScreenTip()
                    }
                    else if (MoreGameManager.instance().isBrowserIosDevice()) {
                        // else {
                        cc.director.emit("eventShowFullScreenTip");
                    }
                    break
                case EnumPopUpPrefabURL.Envelope:
                    this.is_showing = true;
                    window['EnvelopePop'] = true;
                    showEnvelopePop();
                    break;
                case EnumPopUpPrefabURL.VipTip:
                    this.is_showing = true;
                    showVIPTip();
                    break;
                case EnumPopUpPrefabURL.ActivityBonus:
                    this.is_showing = true;
                    showActivityBonus();
                    break;
                case EnumPopUpPrefabURL.PopupBanners:
                    this.is_showing = true;
                    showPopupBanner();
                    break;
                case EnumPopUpPrefabURL.Task:
                    this.is_showing = true;
                    showTaskPop();
                    break;
                case EnumPopUpPrefabURL.LeaderBoardPop:
                    this.is_showing = true;
                    window['LeaderBoardPop'] = true;
                    showRankPop();
                    break;
                case EnumPopUpPrefabURL.LeaderBoardPopJILI:
                    this.is_showing = true;
                    window['LeaderBoardPopJILI'] = true;
                    showRankPopJili();
                    break;
                case EnumPopUpPrefabURL.SpinWheel:
                    this.is_showing = true;
                    window['SpinWheel'] = true;
                    showSpinPop();
                    break;
                case EnumPopUpPrefabURL.RegisterBonus:
                    this.is_showing = true;
                    Global.getInstance().showGetBonusPop();
                    break;
                case EnumPopUpPrefabURL.WalletTask:
                    this.is_showing = true;
                    window['WalletTask'] = true;
                    showWalletTaskPop();
                    break;
                case EnumPopUpPrefabURL.ForceJump:
                    this.is_showing = true;
                    showForceJumpPop();
                    break;
                case EnumPopUpPrefabURL.PwaPop:
                    this.is_showing = true;
                    window['PwaPop'] = true;
                    showPwaPop();
                    break;
                default:
                    this.is_showing = false;
                    break;
            }
        },
        set_isshowing(isshow = false) {
            this.is_showing = isshow
        },
        // 销毁当前弹框，弹出下个弹框
        destroyQueuePopup: function () {
            this.is_showing = false;
            let self = this;
            setTimeout(() => {
                self.autoPopupDialog();
            }, 100);

        },
    }
})();
cc.director.on("DestroyQueuePopup", AutoPopMgr.destroyQueuePopup, AutoPopMgr);