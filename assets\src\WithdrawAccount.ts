import UICommon from "./component/UICommon";
import { ALL_APP_SOURCE_CONFIG } from "./Config";
import Global from "./GlobalScript";
import HttpUtils from "./net/HttpUtils";
import utils from "./utils/utils";
import { uiManager } from "./mgr/UIManager";
import { ACCOUNT_TYPE, DEEP_INDEXZ, UI_PATH_DIC } from "./GlobalConstant";
import { PN_VERIFY_TYPE, showPhoneNumber } from "./SetPhoneNumber";
import { GEETEST_TYPE, GeetestMgr } from "./geetest/GeetestMgr";
import HttpProxy from "./net/HttpProxy";
import { GameData } from "./data/GameData";
import { NetRspMsg, NetRspObject } from "./net/Http";
import { ColorSwitcher } from "./customComponent/ColorSwitcher";

const { ccclass, property } = cc._decorator;

@ccclass
export default class WithdrawAccount extends UICommon {

    @property(cc.ScrollView)
    withdrawScrollView: cc.ScrollView = null;

    @property(cc.ScrollView)
    withdrawTypeScrollView: cc.ScrollView = null;

    @property(cc.Node)
    confirmView: cc.Node = null;

    @property(cc.Node)
    withdrawItemModel: cc.Node = null;

    @property(cc.Node)
    withdrawTypeItemModel: cc.Node = null;

    @property(cc.Node)
    detialsView: cc.Node = null;

    @property(cc.Node)
    selectView: cc.Node = null;

    @property(cc.Sprite)
    detialTypeIcon: cc.Sprite = null;

    @property(cc.Label)
    detailLabAccType: cc.Label = null;

    @property(cc.Label)
    detialTypeLab: cc.Label = null;

    @property(cc.EditBox)
    editPhone: cc.EditBox = null;

    @property(cc.EditBox)
    editfName: cc.EditBox = null;

    @property(cc.EditBox)
    editmName: cc.EditBox = null;

    @property(cc.EditBox)
    editlName: cc.EditBox = null;

    @property(cc.Label)
    labTitle: cc.Label = null;

    @property(cc.Node)
    editPhoneBg: cc.Node = null;

    @property(cc.Node)
    editFnameBg: cc.Node = null;

    @property(cc.Node)
    editLnameBg: cc.Node = null;

    @property([cc.SpriteFrame])
    withdrawTypeSp: cc.SpriteFrame[] = [];
    // LIFE-CYCLE CALLBACKS:

    @property(cc.Node)
    mainNode: cc.Node = null;

    @property(cc.Node)
    select_withdraw: cc.Node = null;

    @property([cc.SpriteFrame])
    unselected_sp: cc.SpriteFrame[] = [];

    @property([cc.SpriteFrame])
    selected_sp: cc.SpriteFrame[] = [];

    withdrawTypeList = null;
    currentDetialsType = "";
    currentDetialsCode = 0;
    editMessage = null;
    checkPhoneCodeSuccess = false;

    MAYA_ACCOUNT_LIMIT = 5;
    GCASH_ACCOUNT_LIMIT = 5;

    WITHDRAW_TYPE = {
        MAYA: 11,
        GCASH: 12
    }

    showType = null;//页面类型

    //提现账号数据和count
    accountData = null;
    accountCount = null;
    mayaCount = null;
    gCashCount = null;

    withdrawTypeItems = [];

    onLoad() {
    }

    init(args) {
        this.showType = args.type;
        if (this.showType == "no_account") {
            this.clickAccountType(true);
        }
        else if (this.showType == "select_payment") {
            this.openSelectPayment({ isNew: true });
        }
    }

    async start() {
        this.detialsView.active = false;
        this.selectView.active = false;
        this.confirmView.active = false;
        this.labTitle.string = "Withdraw Accounts";
        this.editMessage = null;
        this.clearEditInfo();

        await HttpProxy.instance.getRechargeWithdrawConfig();

        this.reqGetWithdrawList()

        let lay = this.mainNode.getChildByName("layout");
        lay.active = false;

        let page = this.node.getChildByName("page").getComponent(cc.PageView);
        page.node.on('page-turning', () => {
            let curIndex = page.getCurrentPageIndex();
            if (curIndex != 1) {
                this.node.destroy();
            }
        }, this);
        let tempnode = this.node.getChildByName("temp");
        page.insertPage(cc.instantiate(tempnode), 0);
        page.scrollToPage(1, 0.01);
    }

    reqGetWithdrawList(cb?) {
        HttpUtils.getInstance().post(3, 3, this, "/common/api/get/withdraw/list", {
            token: Global.getInstance().token,
        }, (response) => {
            if (!this.node || !this.node.isValid) {
                return;
            }
            if (response.data.list && response.data.list.length > 0) {
                this.accountData = response.data.list;
                this.accountCount = response.data.total_num;
                this.mayaCount = 0;
                this.gCashCount = 0;
                for (let index = 0; index < response.data.list.length; index++) {
                    let element = response.data.list[index];
                    if (element.type == this.WITHDRAW_TYPE.MAYA) {
                        this.mayaCount = this.mayaCount + 1;
                    } else if (element.type == this.WITHDRAW_TYPE.GCASH) {
                        this.gCashCount = this.gCashCount + 1;
                    }
                }
            }
            let self = this;
            setTimeout(() => {
                if (self.node && cc.isValid(self.node)) {
                    self.initListView(response);
                }
            }, 1000);

            if (cb) cb();
        });
    }

    initListView(response) {
        let msg = response.data || [];
        let cur_num = msg.current_num || 0;
        let total_num = msg.total_num || 0;
        if (cur_num >= total_num) {
            let lay = this.mainNode.getChildByName("layout");
            lay.active = false;
        } else {
            let lay = this.mainNode.getChildByName("layout");
            lay.active = true;
        }
        if (response.data && response.data.list && response.data.list.length > 0) {
            let list = response.data.list;
            this.withdrawScrollView.node.active = true;
            this.withdrawScrollView.content.removeAllChildren(true);
            for (let index = 0; index < list.length; index++) {
                let element = list[index];
                let model = cc.instantiate(this.withdrawItemModel);
                let icon = model.getChildByName("icon").getComponent(cc.Sprite);
                let lab = model.getChildByName("lab").getComponent(cc.Label);
                let name = model.getChildByName("name").getComponent(cc.Label);
                let number = model.getChildByName("number").getComponent(cc.Label);
                let btn = model.getChildByName("btn");
                let sp_edit = btn.getChildByName("sp_edit");
                let sp_bg = model.getChildByName("masknode").getChildByName("sp_bg");
                let sp_shadow = model.getChildByName("sp_shadow");
                if (element.type == this.WITHDRAW_TYPE.MAYA) {
                    icon.spriteFrame = this.withdrawTypeSp[0];
                    lab.string = "Maya";
                    sp_bg.color = cc.color(1, 212, 106);
                    sp_shadow.color = cc.color(1, 212, 106);
                    if (!GameData.instance.existWithdrawMethod(ACCOUNT_TYPE.MAYA)) {
                        sp_bg.color = cc.color(153, 153, 153);
                        sp_shadow.color = cc.color(153, 153, 153);
                    }
                } else if (element.type == this.WITHDRAW_TYPE.GCASH) {
                    icon.spriteFrame = this.withdrawTypeSp[1];
                    lab.string = "GCash";
                    sp_bg.color = cc.color(72, 129, 237);
                    sp_shadow.color = cc.color(72, 129, 237);
                    if (!GameData.instance.existWithdrawMethod(ACCOUNT_TYPE.GCASH)) {
                        sp_bg.color = cc.color(153, 153, 153);
                        sp_shadow.color = cc.color(153, 153, 153);
                    }
                }

                if (element.first_name && element.last_name) {
                    if (element.middle_name) {
                        let na = element.first_name + " " + element.middle_name + " " + element.last_name;
                        if (na.length > 25) {
                            na = na.substring(0, 25);
                        }
                        name.string = na;
                    } else {
                        let na = element.first_name + " " + element.last_name;
                        if (na.length > 25) {
                            na = na.substring(0, 25);
                        }
                        name.string = na;
                    }
                }

                if (element.is_true) {
                    sp_edit.active = false;
                } else {
                    sp_edit.active = true;
                }

                btn.on(cc.Node.EventType.TOUCH_END, () => {
                    if (element.type == this.WITHDRAW_TYPE.MAYA && !GameData.instance.existWithdrawMethod(ACCOUNT_TYPE.MAYA)) {
                        Global.getInstance().showSimpleTip(Global.getInstance().getLabel("tipword102"));
                        return;
                    }
                    if (element.type == this.WITHDRAW_TYPE.GCASH && !GameData.instance.existWithdrawMethod(ACCOUNT_TYPE.GCASH)) {
                        Global.getInstance().showSimpleTip(Global.getInstance().getLabel("tipword103"));
                        return;
                    }

                    this.editMessage = element;
                    //发送验证码 进行OTP验证
                    if (element.is_true) {
                        Global.getInstance().showSimpleTip("The account that has been successfully Withdrawal cannot be modified.");
                        return;
                    }
                    if (Global.instance.editWdAccSendVerificationCode == 1) {
                        let cb = () => {
                            this.openSelectPayment({ accountType: element.type });
                        }
                        showPhoneNumber(PN_VERIFY_TYPE.ChangeWithdrawAccount, Global.getInstance().userdata.phone, cb, DEEP_INDEXZ.VERIFY_CODE);
                    }
                    else {
                        this.openSelectPayment({ accountType: element.type });
                    }
                })

                number.string = element.account_no.replace('****', '   ****   ');
                model.x = 0;
                model.parent = this.withdrawScrollView.content;
            }

            this.withdrawScrollView.getComponent(cc.Widget).bottom = cur_num >= total_num ? 0 : 273;
            utils.updateAlignment(this.withdrawScrollView.node);
        } else {
            this.withdrawScrollView.node.active = false;
        }
    }

    /**点击Add account */
    clickAccountType(isNew?) {
        //最多可以添加10个提现账号
        if (this.accountData && this.accountData.length >= 10) {
            Global.getInstance().showSimpleTip(`Up to ${this.accountCount} Withdrawal Accounts can be added.`)
            return;
        }
        //用户未设置手机号
        if (!Global.getInstance().userdata.phone) {
            this.hide();
            uiManager.instance.showDialog(UI_PATH_DIC.Preconditions, [{ type: 1 }], null, DEEP_INDEXZ.PRECONDITIONS);
            return;
        }
        //用户未设置支付密码
        if (!Global.getInstance().userdata.withdraw_password) {
            this.hide();
            uiManager.instance.showDialog(UI_PATH_DIC.Preconditions, [{ type: 2 }], null, DEEP_INDEXZ.PRECONDITIONS);
            return;
        }
        //发送验证码 进行OTP验证  
        if (Global.instance.addWdAccSendVerificationCode == 1) {
            let cb = () => {
                this.openSelectPayment({ isNew: true });
            }
            showPhoneNumber(PN_VERIFY_TYPE.AddWithdrawAccount, Global.getInstance().userdata.phone, cb, DEEP_INDEXZ.VERIFY_CODE);
        }
        else {
            this.openSelectPayment({ isNew: true });
        }
    }

    openSelectPayment(option) {
        if (option.isNew) {
            this.editMessage = null;
        }

        HttpProxy.instance.getRechargeWithdrawConfig().then((rsp: NetRspObject) => {
            if (!cc.isValid(this.node)) return;

            if (rsp.msg == NetRspMsg.Success) {
                if (rsp.data && rsp.data.withdraw && rsp.data.withdraw.length > 0) {
                    this.selectView.active = true;
                    this.withdrawTypeList = rsp.data.withdraw;
                    this.initTypeScroll(option);
                } else {
                    this.selectView.active = false;
                    Global.getInstance().showSimpleTip(Global.getInstance().getLabel("tipword105"))
                }
            }
        });
    }

    initTypeScroll(option?) {
        let msg = this.withdrawTypeList;
        if (!msg) {
            return;
        }
        this.withdrawTypeScrollView.content.removeAllChildren(true);

        const defaultAccountType = this.mayaCount >= this.MAYA_ACCOUNT_LIMIT ? this.WITHDRAW_TYPE.GCASH : this.WITHDRAW_TYPE.MAYA;
        let account_type = option?.accountType || defaultAccountType;
        if (msg.length == 1) {
            const element = msg[0];
            account_type = element.account_type;
        }

        for (let index = 0; index < msg.length; index++) {
            let element = msg[index];
            if (this.editMessage) {
                if (this.editMessage.type == this.WITHDRAW_TYPE.MAYA && this.gCashCount >= this.GCASH_ACCOUNT_LIMIT &&
                    element.account_type == this.WITHDRAW_TYPE.GCASH) {
                    continue;
                }
                if (this.editMessage.type == this.WITHDRAW_TYPE.GCASH && this.mayaCount >= this.MAYA_ACCOUNT_LIMIT &&
                    element.account_type == this.WITHDRAW_TYPE.MAYA) {
                    continue;
                }
            } else if ((element.account_type == this.WITHDRAW_TYPE.MAYA && this.mayaCount >= this.MAYA_ACCOUNT_LIMIT) ||
                (element.account_type == this.WITHDRAW_TYPE.GCASH && this.gCashCount >= this.GCASH_ACCOUNT_LIMIT)) {
                continue
            }
            let model = cc.instantiate(this.withdrawTypeItemModel);
            this.withdrawTypeItems.push(model);//保存所有提现类型的model
            let icon = model.getChildByName("add_account_type").getComponent(cc.Sprite);
            let lab = model.getChildByName("lab").getComponent(cc.Label);
            let button = model.getChildByName("button");
            let selected_account = model.getChildByName("selected");
            let unselected_account = model.getChildByName("unselected");
            let model_shadow = model.getChildByName("sp_shadow");
            let model_bg = model.getChildByName("masknode").getChildByName("sp_bg");
            if (element.account_type == this.WITHDRAW_TYPE.MAYA) {//Maya提现
                icon.spriteFrame = this.withdrawTypeSp[0];
                lab.string = "Maya";
                model_bg.color = cc.color(1, 212, 106);
                model_shadow.color = cc.color(1, 212, 106);
            } else if (element.account_type == this.WITHDRAW_TYPE.GCASH) {//GCash提现
                icon.spriteFrame = this.withdrawTypeSp[1];
                lab.string = "GCash";
                model_bg.color = cc.color(72, 129, 237);
                model_shadow.color = cc.color(72, 129, 237);
            }

            const spIndex = element.account_type == this.WITHDRAW_TYPE.MAYA ? 0 : 1;
            selected_account.active = element.account_type == account_type;
            unselected_account.active = element.account_type != account_type;
            unselected_account.getComponent(cc.Sprite).spriteFrame = this.unselected_sp[spIndex];

            if (account_type == element.account_type) {
                this.clearEditInfo();
                this.currentDetialsType = element.account_type == this.WITHDRAW_TYPE.MAYA ? "Maya" : "GCash";
                this.currentDetialsCode = element.account_type;
                this.clickAddAccount(this.currentDetialsType);

                selected_account.getComponent(cc.Sprite).spriteFrame = this.selected_sp[spIndex];
            }

            //统一处理点击事件
            button.on(cc.Node.EventType.TOUCH_END, () => {
                //先取消所有model的选中状态
                this.withdrawTypeItems.forEach(item => {
                    let selected = item.getChildByName("selected");
                    let unselected = item.getChildByName("unselected");
                    selected.active = false;
                    unselected.active = true;
                });

                //再设置当前model为选中状态
                selected_account.active = true;
                unselected_account.active = false;
                if (element.account_type == this.WITHDRAW_TYPE.MAYA) {
                    selected_account.getComponent(cc.Sprite).spriteFrame = this.selected_sp[0];
                } else if (element.account_type == this.WITHDRAW_TYPE.GCASH) {
                    selected_account.getComponent(cc.Sprite).spriteFrame = this.selected_sp[1];
                }
                //更新当前选中的账户信息
                this.clearEditInfo();
                this.currentDetialsType = element.account_type == this.WITHDRAW_TYPE.MAYA ? "Maya" : "GCash";
                this.currentDetialsCode = element.account_type;
                this.clickAddAccount(this.currentDetialsType);
            })

            model.parent = this.withdrawTypeScrollView.content;
        }
        if (504 * msg.length > 1080) {
            this.withdrawTypeScrollView.content.width = 504 * msg.length
        }
    }

    clickAddAccount(userdata) {
        this.labTitle.string = "Enter your accounts details";
        this.detialsView.active = true;
        if (userdata == "Maya") {
            this.detialTypeIcon.spriteFrame = this.withdrawTypeSp[0];
            this.detailLabAccType.string = "Maya";
            this.detialTypeLab.string = "  Maya account no. (09xx xxx xxxx)"
        } else if (userdata == "GCash") {
            this.detialTypeIcon.spriteFrame = this.withdrawTypeSp[1];
            this.detailLabAccType.string = "GCash";
            this.detialTypeLab.string = "  GCash account no. (09xx xxxx xxx)"
        }
        if (this.editMessage) {
            if (this.editMessage.type == this.currentDetialsCode) {
                this.editfName.string = this.editMessage.first_name;
                this.editmName.string = this.editMessage.middle_name;
                this.editlName.string = this.editMessage.last_name;
            } else {
                this.editPhone.string = "";
                this.editfName.string = "";
                this.editmName.string = "";
                this.editlName.string = "";
            }
        }
    }

    clickBackBnt() {
        if (this.detialsView.active) {
            this.labTitle.string = "Withdraw Accounts";
            this.selectView.active = false;
            this.detialsView.active = false;
            //从select account页面进来 需要关闭withdraw account页面
            if (this.showType == "select_payment") {
                this.hide();
            }
            return;
        }

        this.hide();
    }

    showConfirmMessage() {
        let isPhoneRight = utils.isPhilippinePhoneNumber(this.editPhone.string);
        if (!isPhoneRight) {
            Global.getInstance().showSimpleTip("Wrong phone number");
            this.checkPhoneEditEnd();
            return;
        }
        if (this.editfName.string.trim() == "") {
            Global.getInstance().showSimpleTip("First name can not be empty");
            this.checkFnameEditEnd();
            return;
        }
        if (this.editlName.string.trim() == "") {
            Global.getInstance().showSimpleTip("Last name can not be empty");
            this.checkLnameEditEnd();
            return;
        }
        this.confirmView.active = true;
        let lab_msg = utils.getChildByPath(this.confirmView, "node.lab_msg").getComponent(cc.Label);
        let lab_phone = utils.getChildByPath(this.confirmView, "node.lab_phone").getComponent(cc.Label);
        let lab_fname = utils.getChildByPath(this.confirmView, "node.lab_fname").getComponent(cc.Label);
        let lab_mname = utils.getChildByPath(this.confirmView, "node.lab_mname").getComponent(cc.Label);
        let lab_lname = utils.getChildByPath(this.confirmView, "node.lab_lname").getComponent(cc.Label);

        let account_type = utils.getChildByPath(this.confirmView, "node.labnel").getComponent(cc.Label);
        if (account_type) account_type.string = `${this.currentDetialsType} Account No.:`

        lab_msg.string = "Please confirm the " + this.currentDetialsType + " account information below is accurate:"
        lab_phone.string = this.editPhone.string;
        lab_fname.string = utils.cutContent(this.editfName.string, 15);
        lab_mname.string = utils.cutContent(this.editmName.string, 15);
        lab_lname.string = utils.cutContent(this.editlName.string, 15);
    }
    confirmAndReq() {
        let geeid = GEETEST_TYPE.bind_withdraw_account
        if (this.editMessage) {
            geeid = GEETEST_TYPE.change_withdraw_account
        }
        GeetestMgr.instance.geetest_device(geeid, (succ) => {
            if (succ) {
                let ret = {}
                if (Object.getPrototypeOf(succ) !== Boolean.prototype) {
                    ret = succ
                }
                this.confirmAndReq_true(ret);
            }
        })
    }
    confirmAndReq_true(ret?) {
        let gee_guard = ret?.geetest_guard || '';
        let uInfo = ret?.userInfo || '';
        let gee_captcha = ret?.geetest_captcha || '';
        let phone = this.editPhone.string;
        let fname = this.editfName.string;
        let mname = this.editmName.string;
        let lname = this.editlName.string;

        let parms = null;
        let target = ""
        if (this.editMessage) {
            parms = {
                token: Global.getInstance().token,
                account_id: this.editMessage.account_id,
                type: this.currentDetialsCode,
                account_no: phone,
                first_name: fname,
                middle_name: mname || "",
                last_name: lname,
                geetest_guard: gee_guard,
                userInfo: uInfo,
                geetest_captcha: gee_captcha,
                buds: ret?.buds || '64',
            }
            target = "/common/api/update/account/info"
        } else {
            parms = {
                token: Global.getInstance().token,
                type: this.currentDetialsCode,
                account_no: phone,
                first_name: fname,
                middle_name: mname || "",
                last_name: lname,
                geetest_guard: gee_guard,
                userInfo: uInfo,
                geetest_captcha: gee_captcha,
                buds: ret?.buds || '64',
            }
            target = "/common/api/add/withdraw/no"
        }

        HttpUtils.getInstance().post(3, 3, this, target, parms, (response) => {
            if (!this.node || !this.node.isValid) {
                return;
            }
            this.reqGetWithdrawList(() => {
                //添加账号成功
                if (this.showType == "select_payment") {
                    this.hide();
                    let index = 0;//最后一个添加
                    const element = this.accountData[index];
                    let userId = Global.getInstance().userdata.user_id;
                    let accountKey = Global.GLOBAL_STORAGE_KEY.SELECT_ACCOUNT;
                    Global.getInstance().setStoreageData(accountKey + userId, index);
                    // console.log('-------------------,...',this.currentDetialsCode);
                    // console.log('-------------------,...',JSON.stringify(this.accountData));
                    Global.getInstance().setStoreageData("SELECT_SORTTYPE", this.currentDetialsCode + "_" + element.account_id);//点击 缓存选择的账号type和账号id
                    cc.director.emit("select_account", { account_data: this.accountData, select_Index: index, id: element.account_id, type: this.currentDetialsCode });
                }
            });
            this.detialsView.active = false;
            this.selectView.active = false;
            this.confirmView.active = false;
            this.labTitle.string = "Withdraw Accounts";
            this.editMessage = null;
            this.clearEditInfo();
        }, (response) => {
            if (response.msg) {
                Global.getInstance().showSimpleTip(response.msg);
            }
            this.confirmView.active = false;
        });
    }

    cancelMessage() {
        this.confirmView.active = false;
    }

    clearEditInfo() {
        this.editPhone.string = "";
        this.editfName.string = "";
        this.editmName.string = "";
        this.editlName.string = "";
        this.editPhoneBg.getComponent(ColorSwitcher).selectedIndex = 0;
        this.editFnameBg.getComponent(ColorSwitcher).selectedIndex = 0;
        this.editLnameBg.getComponent(ColorSwitcher).selectedIndex = 0;
    }

    checkPhoneEditEnd() {
        let mphone = this.editPhone.string;
        //手机号通用规则修改，允许输入0，则需要判断11位。非0的，则为10位然后系统添加0
        let edit_phone = mphone.substring(0, 1) !== "0" ? "0" + mphone : mphone;
        this.editPhone.string = edit_phone;
        let isPhoneRight = utils.isPhilippinePhoneNumber(this.editPhone.string);
        this.editPhoneBg.getComponent(ColorSwitcher).selectedIndex = isPhoneRight ? 0 : 1;
        this.editPhone.fontColor = isPhoneRight ? cc.color(34, 34, 34, 255) : cc.color(172, 17, 64, 255);
    }

    checkFnameEditEnd() {
        const empty = this.editfName.string.trim() == "";
        this.editFnameBg.getComponent(ColorSwitcher).selectedIndex = !empty ? 0 : 1;
        this.editfName.fontColor = !empty ? cc.color(34, 34, 34, 255) : cc.color(172, 17, 64, 255);
    }

    checkLnameEditEnd() {
        const empty = this.editlName.string.trim() == "";
        this.editLnameBg.getComponent(ColorSwitcher).selectedIndex = !empty ? 0 : 1;
        this.editlName.fontColor = !empty ? cc.color(34, 34, 34, 255) : cc.color(172, 17, 64, 255);
    }
}
