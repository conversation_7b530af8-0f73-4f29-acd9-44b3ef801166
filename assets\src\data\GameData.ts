import { ALL_APP_SOURCE_CONFIG } from "../Config";
import { E_CHANEL_TYPE, EVENT, WALLETTASK_STATUS } from "../GlobalConstant";
import Global from "../GlobalScript";
import utils from "../utils/utils";
import { WalletTaskInfo } from "../Wallet/WalletTaskConst";
import { DownloadTipInfo, forceConfig, globalConfig, pwaConfig, rechargeWithdrawConfig } from "./GameDataConst";

const PWA_KEY = "desktop_shortcut_info";

export class GameData {
    protected static _instance: GameData = null!;
    public static get instance() { return this._instance || (this._instance = new GameData()); }

    // 奖金钱包列表数据
    walletTaskData: WalletTaskInfo[] = null;

    // banner页数据
    bannerData = null;
    popBannerData = null;

    setWalletTaskData(data: WalletTaskInfo[]) {
        data = data || [];

        if (data) {
            const bExpired = (info: WalletTaskInfo) => {
                const status = info.task_status;
                const expireTime = info.expire_time;
                const createdTime = Number(info.created_at);
                const bTimeLimited = expireTime != 0;

                const now = Global.getInstance().now() / 1000;
                let diffTime = expireTime * 3600 + createdTime - now;

                if (bTimeLimited && diffTime <= 0) return true;

                if (status == WALLETTASK_STATUS.EXPIRED || status == WALLETTASK_STATUS.DELETE) return true;

                return false;
            }

            this.walletTaskData = data.filter((element: WalletTaskInfo) => { return !bExpired(element) });
            this.walletTaskData.sort((a: WalletTaskInfo, b: WalletTaskInfo) => { return b.task_status - a.task_status });
        }
    }

    reduceWalletTask(taskId: string) {
        this.walletTaskData = this.walletTaskData.filter((item: WalletTaskInfo) => { return item.id != taskId });
        this.saveWalletTaskData();

        dispatch(EVENT.UPDATE_WALLETTASKINFO);
    }

    saveWalletTaskData() {
        const userId = Global.instance.userdata.user_id;
        const idUniq = userId + "_" + Global.GLOBAL_STORAGE_KEY.WALLET_TASK_INFO;
        Global.instance.setStoreageData(idUniq, JSON.stringify(this.walletTaskData));
    }

    /** 进入平台类型 true：进入平台 false：切回平台 */
    private _enterHomeType: boolean = true;
    set enterHomeType(enterHomeType: boolean) {
        this._enterHomeType = enterHomeType;
    }
    get enterHomeType() {
        return this._enterHomeType;
    }

    /** 每日首次进入平台，有任务 */
    existWalletTask() {
        const idUniq = Global.getInstance().userdata.user_id + "_" + Global.GLOBAL_STORAGE_KEY.WALLET_TASK_DAY;
        const day = Global.instance.getStoreageData(idUniq);
        const today = utils.getToday();
        if (day == today) return false;

        Global.instance.setStoreageData(idUniq, today);

        if (this.walletTaskData && this.walletTaskData.length > 0) {
            this.saveWalletTaskData();
            return true;
        }

        return false;
    }

    /** 进入平台，有新任务 */
    existNewWalletTask() {
        let hasNewTask = false;

        const userId = Global.instance.userdata.user_id;
        const idUniq = userId + "_" + Global.GLOBAL_STORAGE_KEY.WALLET_TASK_INFO;
        const storeageData = Global.instance.getStoreageData(idUniq);
        if (storeageData) {
            const data: WalletTaskInfo[] = JSON.parse(storeageData);

            this.walletTaskData.forEach((item: WalletTaskInfo) => {
                let exist = false;
                data.forEach((storageItem: WalletTaskInfo) => {
                    if (storageItem.id == item.id) exist = true;
                })

                if (!exist) hasNewTask = true;
            })
        }
        else if (this.walletTaskData && this.walletTaskData.length > 0) {
            hasNewTask = true;
        }

        this.saveWalletTaskData();

        return hasNewTask;
    }

    getWalletTaskRewardAmount() {
        if (this.walletTaskData) {
            let amount = 0;
            this.walletTaskData.forEach((element: WalletTaskInfo) => {
                if (element.task_status == WALLETTASK_STATUS.FINISHED) amount += Number(element.bonus);
            });

            return amount;
        }
        return 0;
    }

    existOngoingTask() {
        let exist = false;
        if (this.walletTaskData) {
            this.walletTaskData.forEach((element: WalletTaskInfo) => {
                if (element.task_status == WALLETTASK_STATUS.ONGOING) exist = true;
            });
        }
        return exist;
    }

    receiveWalletTask(taskId: string) {
        if (this.walletTaskData) {
            this.walletTaskData.forEach((element: WalletTaskInfo) => {
                if (element.id == taskId) element.task_status = WALLETTASK_STATUS.ONGOING;
            });

            this.walletTaskData.sort((a: WalletTaskInfo, b: WalletTaskInfo) => { return b.task_status - a.task_status });
        }

        this.saveWalletTaskData();
    }

    receiveWalletTaskReward(taskId: string) {
        if (this.walletTaskData) {
            this.walletTaskData = this.walletTaskData.filter((element: WalletTaskInfo) => {
                return element.id != taskId;
            });
        }

        this.saveWalletTaskData();
    }

    setBannerData(data) {
        if (data) {
            this.bannerData = data.banner;
            this.popBannerData = data.pop_banner;
        }
    }

    private _downloadTipData: DownloadTipInfo = null;
    set downloadTipData(data: any) {
        const guideInfoArray = data?.data;
        if (!guideInfoArray) return;

        this._downloadTipData = guideInfoArray[0];
        for (let i = 0; i < guideInfoArray.length; i++) {
            const info = guideInfoArray[i];
            if (ALL_APP_SOURCE_CONFIG.channel == E_CHANEL_TYPE.G_CASH && info.channel == '256') {
                this._downloadTipData = info;
                break;
            }
        }
    }
    get downloadTipData() { return this._downloadTipData; }

    private _rechargeWithdrawConfig: rechargeWithdrawConfig = null;
    set rechargeWithdrawConfig(data: rechargeWithdrawConfig) { this._rechargeWithdrawConfig = data; }
    get rechargeWithdrawConfig() { return this._rechargeWithdrawConfig; }

    existDepositMethod() {
        let exist = false;
        const rechargeConfig = this._rechargeWithdrawConfig.recharge;
        if (rechargeConfig?.length > 0) exist = true;

        return exist;
    }

    existWithdrawMethod(accountType: number) {
        let exist = false;
        const withdrawConfig = this._rechargeWithdrawConfig.withdraw;
        withdrawConfig?.forEach(element => {
            if (element.account_type == accountType) exist = true;
        });

        return exist;
    }

    private _globalConfig: globalConfig[] = null;
    set globalConfig(data: globalConfig[]) { this._globalConfig = data; }
    get globalConfig() { return this._globalConfig; }
    getGlobalConfig(key: string) {
        const array = this._globalConfig?.filter((elem: globalConfig) => {
            return elem.classify == key;
        })
        if (array) return array[0];
    }

    getPwaConfig() {
        const pwaConfig = this.getGlobalConfig(PWA_KEY);
        if (!pwaConfig) return;

        const value: pwaConfig = JSON.parse(pwaConfig.value);
        return value;
    }

    private _forceConfig: forceConfig = null;
    set forceConfig(data: forceConfig) { this._forceConfig = data; }
    get forceConfig() { return this._forceConfig; }
}