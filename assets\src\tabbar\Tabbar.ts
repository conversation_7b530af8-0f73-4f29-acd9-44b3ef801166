// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html

import { ALL_APP_SOURCE_CONFIG } from "../Config";
import { GameData } from "../data/GameData";
import GameControl from "../GameControl";
import { E_CHANEL_TYPE, E_PAGE_TYPE, UI_PATH_DIC } from "../GlobalConstant";
import Global from "../GlobalScript";
import AllGameView from "../hall/AllGameView";
import { ACTIVITY_TYPE } from "../hall/Promo";
import { LOGIN_WAY } from "../login/phonePasswordLogin";
import { AutoPopMgr } from "../mgr/AutoPopMgr";
import { uiManager } from "../mgr/UIManager";
import MoreGameManager from "../MoreGameManager";
import { GlobalEnum } from "../room/GlobalEnum";
import utils from "../utils/utils";

const { ccclass, property } = cc._decorator;
const REDIRECT_PAGE = {
    'game': 'game',//type 游戏 分类 带 id
    'promos': 'promos',//点击广告条 带的参数
    'vip': 'vip',//点击vip
    'all': 'all',//更多游戏
    'service': 'service',//客服
    'bet': 'bet',//go bet 点击之后的逻辑
    'banner': 'banner',//banner跳转
    'pay': 'pay',//支付后的回掉
    'mayawebpay': 'mayawebpay', //mayawebpay 提现成功
}
@ccclass
export default class NewClass extends cc.Component {

    //四个不同的button node
    @property(cc.Node)
    btnHome: cc.Node = null;

    @property(cc.Node)
    btnPromo: cc.Node = null;

    @property(cc.Node)
    btnNews: cc.Node = null;

    @property(cc.Node)
    btnProfile: cc.Node = null;

    //这里设置 视频区域 不显示 为的就是底部遮挡
    @property(cc.Node)
    bg_node: cc.Node = null;

    //tabbar node底部
    @property(cc.Node)
    tabbar_node: cc.Node = null;

    //放置四个页面用的node
    @property(cc.Node)
    prefabNode: cc.Node = null;

    @property([cc.SpriteFrame])
    bottomBarSpriteFrames: cc.SpriteFrame[] = [];

    //promoClickMark
    promoClickMark: boolean = false;

    //默认 点击的是home页面
    currentMainTab = "";
    //第一次 防止重复 弹出转盘
    show_spin_auto = false;
    //四个 prefab
    mProfilePrefab: cc.Prefab = null;
    mNewsPrefab: cc.Prefab = null;
    mPromoPrefab: cc.Prefab = null;
    mHallPrefab: cc.Prefab = null;
    mPlayerPrefab: cc.Prefab = null;
    //4个页面
    mProfileView: cc.Node = null;
    mNewsView: cc.Node = null;
    mPromoView: cc.Node = null;
    mHallView: cc.Node = null;
    mPlayerView: cc.Node = null;
    // LIFE-CYCLE CALLBACKS:

    //计算出 显示那个页面
    re_page = null
    re_page_params = null//带的第二个参数 如果有就用 这个目前就 首页的 游戏类型使用到
    re_page_params3 = null//带的第三个参数 
    //添加第五个 直播视频
    isLoadingTabbar = [false, false, false, false, false];//对应下面4个 tabbar 是否加载中 防止多次加载 lemoon 解决底部不对应问题

    banner_data = null;//存贮banner数据

    onLoad() {
        let gamescene = cc.director.getScene().getComponentInChildren(GameControl);
        gamescene.mTabbar = this;
        cc.director.on("ReturnToHome", this.tap_bar, this);
        //改变tabbar 状态
        cc.director.on("change_tabbar_state", this.refresh_bottom_btn, this);

        this.re_page = utils.getBrowserValue("type");
        this.re_page_params = utils.getBrowserValue("id");
        this.re_page_params3 = utils.getBrowserValue("amount");
    }

    start() {
        this.initBottomBar();
        this.start_true();
    }
    //兼容测试模式 真正开始
    start_true() {
        if (ALL_APP_SOURCE_CONFIG.channel == E_CHANEL_TYPE.MAYA || ALL_APP_SOURCE_CONFIG.channel == E_CHANEL_TYPE.G_CASH) {
            AutoPopMgr.autoPopupDialog(true);
        }
        if (Global.instance.isNeedPwaPop()) {
            AutoPopMgr.autoPopupDialog(true);
        }

        let key_s = this.re_page || 'home'
        switch (key_s) {
            case REDIRECT_PAGE.game:
                this.tap_bar(null, 'home')
                //正常进入 首页
                break;
            case REDIRECT_PAGE.service:
                //稍后跳到 客服页面
                this.tap_bar(null, 'account')
                this.re_page_params = 'service'
                break;
            case REDIRECT_PAGE.all:
                //稍后跳到更多游戏 第一个moregame
                // this.showAllGamesSerchView()
                this.tap_bar(null, 'home')
                break;
            case REDIRECT_PAGE.promos:
                this.tap_bar(null, 'promos')
                break;
            case REDIRECT_PAGE.vip:
                this.tap_bar(null, 'account')
                break;
            case REDIRECT_PAGE.bet:
                this.clickGoBet()
                break;
            case REDIRECT_PAGE.banner:
                this.tap_bar(null, 'banner')
                break;
            case REDIRECT_PAGE.pay:
                if (this.re_page_params == 'success') {
                    this.clickGoBet();
                } else {
                    this.tap_bar(null, 'account');
                }
                break;
            case REDIRECT_PAGE.mayawebpay:
                this.tap_bar(null, 'account');

                let str = "Your withdrawal request has been submitted successfully. You will receive in your account within 10 minutes.";
                const state = utils.getBrowserValue("state");
                if (state == "failed") str = "Your withdrawal request has been submitted failed.";
                Global.getInstance().showCommonTip2({ word: str, confirm: "Done" }, this, true);
                break;
            default:
                this.tap_bar(null, 'home')
                break;
        }
        // 获取当前地址（不带参数和哈希）
        var cleanUrl = window.location.origin + window.location.pathname;
        // 替换地址栏
        window.history.replaceState({}, document.title, cleanUrl);
    }
    initBottomBar() {
        let tabs = Global.getInstance().getHallTabs();
        if (!tabs || tabs.length == 0) {
            return;
        }
        //这里设置 自动排列 
        this.btnHome.active = false;
        this.btnPromo.active = false;
        this.btnNews.active = false;
        this.btnProfile.active = false;
        let btntNode = [];

        if (tabs["home"]) {
            this.btnHome.active = true;
            btntNode.push(this.btnHome);
        }
        if (tabs["activity"]) {
            this.btnPromo.active = true;
            btntNode.push(this.btnPromo);
        }
        if (tabs["news"]) {
            this.btnNews.active = true;
            btntNode.push(this.btnNews);
        }
        if (tabs["center"]) {
            this.btnProfile.active = true;
            btntNode.push(this.btnProfile);
        }
        let gapWidth = cc.view.getDesignResolutionSize().width / btntNode.length;
        for (let index = 0; index < btntNode.length; index++) {
            let element = btntNode[index];
            let gap = (cc.winSize.width - cc.view.getDesignResolutionSize().width) / 2;
            element.position = new cc.Vec3(gapWidth + gapWidth * index - cc.winSize.width / 2 - gapWidth / 2 + gap, 0);
        }
    }
    clickGoBet(cb?, markopen?) {
        //点击Bet Now 跳转至游戏 History 页面
        MoreGameManager.instance().queryHistoryGameList(() => {
            let historyList = MoreGameManager.instance().getHistoruGameData();
            //如果 History 页面无数据，跳转至 Like 页，若 Like 页也无数据。跳转至首页 Top Game 的位置
            if (historyList.length > 0) {
                this.showAllGamesSerchView('History', markopen);
                cb && cb();
            } else {
                MoreGameManager.instance().queryLikeGameList(() => {
                    let likeList = MoreGameManager.instance().getLikeGameData();
                    if (likeList.length > 0) {
                        this.showAllGamesSerchView('Like', markopen);
                        cb && cb();
                    } else {
                        this.tap_bar(null, 'home');
                        cb && cb();
                    }
                });
            }
        });
    }
    showAllGamesSerchView(ctype: string, markopen?) {
        let currentType = ctype || ''
        if (currentType == '') {
            let gameTypes = Global.getInstance().getGameType();
            // gameTypes = [{"id":10000,"game_type":"SLOTS","sort":"5"},{"id":10006,"game_type":"CASINO","sort":"6"},
            // {"id":10001,"game_type":"POKER","sort":"4"},{"id":10004,"game_type":"BINGO","sort":"3"},
            // {"id":10002,"game_type":"FISHING","sort":"2"},{"id":10005,"game_type":"SPORTS","sort":"1"}]
            for (let index = 0; index < gameTypes.length; index++) {
                let element = gameTypes[index];
                let str = element.game_type;
                if (str && str.length > 0) {
                    let type = str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
                    if (index == 0) {
                        //默认第一个 高亮
                        currentType = type;
                    }
                }
            }
        }
        uiManager.instance.loadPrefabByLoading(UI_PATH_DIC.AllGameView, cc.Prefab, () => {
        }, (err, prefab: any) => {
            if (err) {
                return;
            }
            let node = cc.instantiate(prefab);
            node.parent = Global.instance.popNode;
            node.position = new cc.Vec3(0, 0);
            node.getComponent(AllGameView).gameDataList(currentType, () => {

            });
            if (markopen) {

            }
        });
    }
    //设置红点promos
    setPromos_redpoint(isshow = false) {
        let redpoint = this.btnPromo.getChildByName("redpoint");
        redpoint.active = isshow;
    }
    //设置红点 news
    setNews_redpoint(isshow = false) {
        let newPoint = this.btnNews.getChildByName("redpoing");
        newPoint.active = isshow;
    }
    //如果这里是banner的时候 关闭banner之后打开首页
    rein_home() {
        if (this.currentMainTab == 'banner') {
            this.tap_bar(null, 'home')
        }
    }
    //点击不同的 bar 0123 代表不同的点击位置
    tap_bar(event: any, datastr: string) {
        let gamescene = cc.director.getScene().getComponentInChildren(GameControl);
        if (this.currentMainTab == 'home') {
            gamescene.mHall && gamescene.mHall.showLiveVideo();
        }
        if (this.currentMainTab == datastr) return;
        //点击是否 打开登录页面
        let open_login = false;
        if (!Global.getInstance().token && datastr == "account") open_login = true;
        if (!Global.getInstance().token && datastr == "games") open_login = true;
        if (open_login) {
            if (this.promoClickMark) {
                //未登录 跳转登录页
                uiManager.instance.showDialog(UI_PATH_DIC.phonePasswordLogin, [{ way: LOGIN_WAY.PhoneCode, curPromoPage: true }])
            } else {
                //未登录 跳转登录页
                uiManager.instance.showDialog(UI_PATH_DIC.phonePasswordLogin, [{ way: LOGIN_WAY.PhoneCode, curPromoPage: false }])
            }
            return;
        }
        gamescene.mHall && gamescene.mHall.hideLiveVideo();
        if (datastr == 'home') GameData.instance.enterHomeType = this.currentMainTab == '';
        this.currentMainTab = datastr;
        this.showHideSpinButton(datastr);
        this.isShowEnvelopeBtn(datastr);
        switch (datastr) {
            case 'home':
                this.showHallview();
                //hall
                // this.refresh_bottom_btn();
                break;
            case 'promos':
                //promos
                this.showPromoview();
                break;
            case 'news':
                //news
                this.showNewsview();
                break;
            case 'account':
                //account
                this.showProfileview();
                gamescene.mHall && gamescene.mHall.reqInboxMsg();
                Global.getInstance().updateBalanceAfterGame();
                break;
            case 'games':
                this.showAllGamesSerchView('');
                break;
            case 'banner':
                //bug问题修复
                this.showBannerJump();
                break;
            default:
                this.refresh_bottom_btn();
                break;
        }
    }

    //banner跳转
    showBannerJump() {
        Global.getInstance().reqBannerData(() => {
            this.banner_data = Global.getInstance().getBannerData();
            if (this.banner_data.length > 0) {
                let target_banner = this.banner_data.filter(ele => ele.id == this.re_page_params);
                if (target_banner.length > 0) {
                    let element = target_banner[0];
                    if (element.home_page_jump == 1) {//是否跳转promo页面 home_page_jump 1:promo 2:jump type mode
                        this.showPromoview();
                    } else if (element.home_page_jump == 2) {
                        Global.getInstance().bannerJumpView(element, "banner");
                    }
                }
            }
        })
    }


    //刷新下面的 button 显示
    refresh_bottom_btn() {
        //如果没有加载出来 则保持不变 修改顽固bug 显示跟 tabbar 不一致
        let isreturn = false;
        switch (this.currentMainTab) {
            case "promos":
                if (!this.mPromoView) isreturn = true;
                break;
            case "news":
                if (!this.mNewsView) isreturn = true;
                break;
            case "account":
                if (!this.mProfileView) isreturn = true;
                break;
            case "home":
                if (!this.mHallView) isreturn = true;
                break;
            default:
                break;
        }
        if (isreturn) {
            return;
        }
        let btn_home = utils.getChildByPath(this.node, "bottom_bar.btn_home.Background").getComponent(cc.Sprite);
        let btn_promo = utils.getChildByPath(this.node, "bottom_bar.btn_promo.Background").getComponent(cc.Sprite);
        let btn_news = utils.getChildByPath(this.node, "bottom_bar.btn_news.Background").getComponent(cc.Sprite);
        let btn_account = utils.getChildByPath(this.node, "bottom_bar.btn_account.Background").getComponent(cc.Sprite);

        btn_home.spriteFrame = this.bottomBarSpriteFrames[0];
        btn_promo.spriteFrame = this.bottomBarSpriteFrames[1];
        btn_news.spriteFrame = this.bottomBarSpriteFrames[2];
        btn_account.spriteFrame = this.bottomBarSpriteFrames[3];

        if (this.mProfileView) this.mProfileView.active = false;
        if (this.mNewsView) this.mNewsView.active = false;
        if (this.mPromoView) this.mPromoView.active = false;
        // if (this.mHallView) this.mHallView.active = false;
        let gamescene = cc.director.getScene().getComponentInChildren(GameControl);
        Global.getInstance().setPageId(E_PAGE_TYPE.OTHER);
        switch (this.currentMainTab) {
            case "home":
                if (this.mHallView) this.mHallView.active = true;
                btn_home.spriteFrame = this.bottomBarSpriteFrames[4];
                Global.getInstance().setPageId(E_PAGE_TYPE.HALL);
                gamescene.mHall && gamescene.mHall.reqInboxMsg();
                gamescene.mHall && gamescene.mHall.showLiveVideo();
                // gamescene.mHall && gamescene.mHall.reqWalletTaskMsg();
                this.promoClickMark = false;
                Global.getInstance().updateBalanceAfterGame();
                if (this.show_spin_auto) {
                    cc.director.emit('check_show_spin')
                }
                this.show_spin_auto = true;
                break;
            case "promos":
                if (this.mPromoView) this.mPromoView.active = true;
                btn_promo.spriteFrame = this.bottomBarSpriteFrames[5];
                cc.director.emit("Hide_LiveVideo");
                cc.director.emit('check_show_spin')
                break;
            case "news":
                if (this.mNewsView) this.mNewsView.active = true;
                btn_news.spriteFrame = this.bottomBarSpriteFrames[6];
                cc.director.emit("Hide_LiveVideo");
                cc.director.emit('check_show_spin')
                break;
            case "account":
                if (this.mProfileView) this.mProfileView.active = true;
                let isVip = Global.getInstance().userdata?.is_vip || 0;
                if (parseInt(isVip)) {//VIP用户
                    btn_account.spriteFrame = this.bottomBarSpriteFrames[8];
                } else {//非VIP用户
                    btn_account.spriteFrame = this.bottomBarSpriteFrames[7];
                }

                cc.director.emit("Hide_LiveVideo");
                break;
            default:
                btn_home.spriteFrame = this.bottomBarSpriteFrames[4];
                break;
        }
    }
    //判断是否显示转盘按钮 在account页面不显示
    isShowwheel_btn() {
        //第一次请求的时候 用到 防止在其他页面
        let btn_spin = Global.getInstance().popNode.getChildByName("btn_spin");
        if (!btn_spin) return;
        if (this.currentMainTab == 'account') {
            btn_spin.active = false;
        } else {
            if (Global.getInstance().spinInfo.is_start == 1) {
                btn_spin.active = true;
            }
        }
    }

    //判断是否展示首充红包入口按钮
    isShowEnvelopeBtn(type) {
        Global.getInstance().reqEnvelopeData(() => {
            let btn_envelope = Global.getInstance().popNode.getChildByName("btn_envelope");
            if (!btn_envelope) return;
            if (type == 'home' && parseInt(Global.getInstance().envelopeInfo.first_deposit_bonus_guide_is_start) == 1 && !Global.getInstance().envelopeInfo.is_first_recharge) {
                btn_envelope.active = true;
                btn_envelope.zIndex = 1;
            } else {
                btn_envelope.active = false;
            }
        })
    }

    showPromoview() {
        if (this.mPromoView) {
            this.mPromoView.active = true;
            this.refresh_bottom_btn();
            return;
        }
        let cb = () => {
            this.mPromoView = cc.instantiate(this.mPromoPrefab);
            this.mPromoView.position = new cc.Vec3(0, 0, 14);
            this.mPromoView.zIndex = 4
            this.mPromoView.parent = this.prefabNode;
        }
        if (this.mPromoPrefab == null) {
            if (this.isLoadingTabbar[1]) {
                uiManager.instance.loadPrefabLoading_noback();//显示加载 重新
                return;
            }
            this.isLoadingTabbar[1] = true;
            uiManager.instance.loadPrefabByLoading(UI_PATH_DIC.Promo, cc.Prefab, () => {
            }, (err, prefab: any) => {
                if (err) {
                    console.error('-----这里加载promo 失败了')
                    this.isLoadingTabbar[1] = false;
                    return;
                }
                this.mPromoPrefab = prefab;
                cb();
            });
        } else {
            cb();
        }
    }

    showNewsview() {
        if (this.mNewsView) {
            this.mNewsView.active = true;
            this.refresh_bottom_btn();
            return;
        }
        let cb = () => {
            this.mNewsView = cc.instantiate(this.mNewsPrefab);
            this.mNewsView.position = new cc.Vec3(0, 0, 13);
            this.mNewsView.zIndex = 4
            this.mNewsView.parent = this.prefabNode;
        }
        if (this.mNewsPrefab == null) {
            if (this.isLoadingTabbar[2]) {
                uiManager.instance.loadPrefabLoading_noback();//显示加载 重新
                return;
            }
            this.isLoadingTabbar[2] = true;
            uiManager.instance.loadPrefabByLoading(UI_PATH_DIC.NewView, cc.Prefab, () => {
            }, (err, prefab: any) => {
                if (err) {
                    console.error('-----这里加载news 失败了')
                    this.isLoadingTabbar[2] = false;
                    return;
                }
                this.mNewsPrefab = prefab;
                cb();
            });
        } else {
            cb();
        }
    }

    showProfileview() {
        if (this.mProfileView) {
            this.mProfileView.active = true;
            this.refresh_bottom_btn();
            return;
        }
        let cb = () => {
            this.mProfileView = cc.instantiate(this.mProfilePrefab);
            this.mProfileView.position = new cc.Vec3(0, 0, 12);
            this.mProfileView.parent = this.prefabNode;
            this.mProfileView.zIndex = 4
        }
        if (this.mProfilePrefab == null) {
            if (this.isLoadingTabbar[3]) {
                uiManager.instance.loadPrefabLoading_noback();//显示加载 重新
                return;
            }
            this.isLoadingTabbar[3] = true;
            uiManager.instance.loadPrefabByLoading(UI_PATH_DIC.UserProfileSetting, cc.Prefab, () => {
            }, (err, prefab: any) => {
                if (err) {
                    this.isLoadingTabbar[3] = false;
                    console.error('-----这里加载profileview 失败了')
                    return;
                }
                this.mProfilePrefab = prefab;
                cb();
            });
        } else {
            cb();
        }
    }

    //显示hall页面
    showHallview() {
        if (this.mHallView) {
            this.mHallView.active = true;
            this.refresh_bottom_btn();
            return;
        }
        let cb = () => {
            this.mHallView = cc.instantiate(this.mHallPrefab);
            this.mHallView.position = new cc.Vec3(0, 0, 10);
            this.mHallView.parent = this.prefabNode;
            this.bg_node.active = false
            this.mHallView.zIndex = 2
        }
        if (this.mHallPrefab == null) {
            if (this.isLoadingTabbar[0]) {
                uiManager.instance.loadPrefabLoading_noback();//显示加载 重新
                return;
            }
            this.isLoadingTabbar[0] = true;
            uiManager.instance.loadPrefabByLoading(UI_PATH_DIC.Hall, cc.Prefab, () => {
            }, (err, prefab: any) => {
                if (err) {
                    this.isLoadingTabbar[0] = false;
                    console.error('-----这里加载hall 失败了')
                    return;
                }
                this.mHallPrefab = prefab;
                cb();
            });
        } else {
            cb();
        }
    }
    //隐藏hall页面
    hideHallview() {
        if (this.mHallView) {
            this.mHallView.active = false;
            return;
        }
    }
    // update (dt) {}

    showHideSpinButton(type) {
        let btn_spin = Global.getInstance().popNode.getChildByName("btn_spin");
        if (!btn_spin) return;
        if (type == 'account' || type == 'video') {
            btn_spin.active = false;
        } else {
            if (Global.getInstance().spinInfo.is_start == 1) {
                btn_spin.active = true;
                btn_spin.zIndex = 1;
            }
        }
    }

    //判断是否是hall页面
    isHall_show() {
        return this.currentMainTab == 'home'
    }

    hideVideoPlayer() {
        if (this.mHallView) this.mHallView.active = true;
        this.tabbar_node.active = true;
        if (this.mPlayerView) this.mPlayerView.active = false;
        //打开 转盘按钮 和首充按钮
        this.showHideSpinButton('home');
        this.isShowEnvelopeBtn('home');
        let gamescene = cc.director.getScene().getComponentInChildren(GameControl);
        gamescene.mHall.showLiveVideo();
    }
    //显示视频横屏 
    showVideoPlayer() {
        let cb_later = () => {
            //隐藏所有的区域
            if (this.mProfileView) this.mProfileView.active = false;
            if (this.mNewsView) this.mNewsView.active = false;
            if (this.mPromoView) this.mPromoView.active = false;
            if (this.mHallView) this.mHallView.active = false;
            //防止 播放视频的时候 弹出其他页面弹窗之类的
            let gamescene = cc.director.getScene().getComponentInChildren(GameControl);
            gamescene.mHall.hideLiveVideo();
            Global.getInstance().setPageId(E_PAGE_TYPE.OTHER);
            this.showHideSpinButton('video');
            this.isShowEnvelopeBtn('video');
            this.tabbar_node.active = false;

        }
        if (this.mPlayerView) {
            this.mPlayerView.active = true;
            cb_later()
            return;
        }
        let cb = () => {
            this.mPlayerView = cc.instantiate(this.mPlayerPrefab);
            this.mPlayerView.position = new cc.Vec3(0, 0, 100);
            this.mPlayerView.parent = this.prefabNode;
            this.bg_node.active = false
            this.mPlayerView.zIndex = 3
            cb_later()
        }
        if (this.mPlayerPrefab == null) {
            if (this.isLoadingTabbar[4]) {
                uiManager.instance.loadPrefabLoading_noback();//显示加载 重新
                return;
            }
            this.isLoadingTabbar[4] = true;
            uiManager.instance.loadPrefabByLoading(UI_PATH_DIC.VideoPlayer, cc.Prefab, () => {
            }, (err, prefab: any) => {
                if (err) {
                    this.isLoadingTabbar[4] = false;
                    console.error('-----这里加载video 失败了')
                    return;
                }
                this.mPlayerPrefab = prefab;
                cb();
            });
        } else {
            cb();
        }
    }

    // 测试 
    // let gamescene = cc.director.getScene().getComponentInChildren(GameControl);
    // gamescene.mTabbar.goHall_showCatogry(['10004']);
    // 显示hall 页面 并且跳转到对应游戏分类 不传或者null 跳转第一个 '10004'
    goHall_showCatogry(gameTypeList: string[]) {
        const gametype = Global.getInstance().getGameType();
        const idArray = gametype.map((item) => { return item.id; });
        const finalIdArray = idArray.filter(item => gameTypeList.includes(item));

        let ctype = finalIdArray.length > 0 ? finalIdArray[0] : idArray[0];

        //第一步 跳转到hall页面
        this.tap_bar(null, 'home');

        setTimeout(() => {
            //防止 播放视频的时候 弹出其他页面弹窗之类的
            let gamescene = cc.director.getScene().getComponentInChildren(GameControl);
            gamescene.mHall.gameTpyeButtonClickListener(null, ctype);
        }, 100);
    }

    // 测试 
    // let gamescene = cc.director.getScene().getComponentInChildren(GameControl);
    // gamescene.mTabbar.showAllGames_containProviders(['1']);
    // 这里传的厂商id数组['1','2']
    showAllGames_containProviders(providerList: string[]) {
        Global.instance.getGameProvider(async (provider) => {
            // 某个类型下是否有某个厂商的游戏
            const hasGame = (gameType: string, company: number) => {
                return new Promise((resolve, reject) => {
                    let gamesByType = MoreGameManager.instance().getThirdGameDataByType(gameType);
                    if (gamesByType && gamesByType.length > 0) {
                        const gameDatas = MoreGameManager.instance().getThirdGameDataWithCompany(company, gameType);
                        resolve(gameDatas.length > 0);
                        return;
                    }

                    MoreGameManager.instance().doQueryGameListByTag(() => {
                        const gameDatas = MoreGameManager.instance().getThirdGameDataWithCompany(company, gameType);
                        resolve(gameDatas.length > 0);
                    }, gameType);
                });
            };

            let cutProvider = provider.filter((item) => { return providerList.includes(item.id.toString()) && item.status != 3; });
            cutProvider = cutProvider.map((item) => { return item.id.toString(); });
            const gameType = Global.instance.getGameType();
            let gtype = '10006';
            for (let i = 0; i < gameType.length; i++) {
                let exist = false;

                const type = gameType[i].id;
                for (let j = 0; j < cutProvider.length; j++) {
                    const company = cutProvider[j];
                    const bExist = await hasGame(type, company);
                    if (bExist) {
                        gtype = type;
                        exist = true;
                    }
                }

                if (exist) break;
            }

            this.showAllGames_gtype_providers(gtype, cutProvider);
        });

        // let currentType = ''//这是要跳转的分类
        // let c_provider = cprovider;
        // if (currentType == '') {
        //     let gameTypes = Global.getInstance().getGameType();
        //     // gameTypes = [{"id":10000,"game_type":"SLOTS","sort":"5"},{"id":10006,"game_type":"CASINO","sort":"6"},
        //     // {"id":10001,"game_type":"POKER","sort":"4"},{"id":10004,"game_type":"BINGO","sort":"3"},
        //     // {"id":10002,"game_type":"FISHING","sort":"2"},{"id":10005,"game_type":"SPORTS","sort":"1"}]
        //     Global.getInstance().getGameProvider((provider: any) => {
        //         let providerItemsList = provider;
        //         outloop:
        //         for (let index_gtype = 0; index_gtype < gameTypes.length; index_gtype++) {
        //             const element_gtype = gameTypes[index_gtype];
        //             const str = element_gtype.game_type;
        //             if (str && str.length > 0) {
        //                 const type_c = str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
        //                 if (cprovider && cprovider.length > 0) {
        //                     //这里循环是否包含 数组里的游戏分类
        //                     for (let index = 0; index < providerItemsList.length; index++) {
        //                         let element = providerItemsList[index];
        //                         // {"id": 3,"provider": "Evo","short_name": "","icon_home": "images\/daef73b18c6e52061a2c614ee0a3f9fb.png",
        //                         //     "icon_other": "images\/63d11ff4929c045a20228906748c0caa.png",
        //                         //     "game_type": [ "10001","10004","10006"],"status": 1,"content": ""},
        //                         //1 正常  2维护  3隐藏
        //                         if (element.status != 3) {
        //                             //首先在这个分类下
        //                             if (element.game_type.includes((element_gtype.id).toString())) {
        //                                 console.log('--------------provider:', element.provider)
        //                                 //如果 有相同元素 其次
        //                                 if (cprovider.indexOf(element.provider) != -1) {
        //                                     currentType = type_c;
        //                                     break outloop;
        //                                 }
        //                             }
        //                         }
        //                     }
        //                 } else {
        //                     if (index_gtype == 0) {
        //                         //默认第一个 高亮
        //                         currentType = type_c;
        //                         c_provider = [];//传空 默认全显示第一个分类
        //                     }
        //                 }
        //             }
        //         }
        //     });
        //     console.log('----currenttype:', currentType);
        // }
        // uiManager.instance.loadPrefabByLoading(UI_PATH_DIC.AllGameView, cc.Prefab, () => {
        // }, (err, prefab: any) => {
        //     if (err) {
        //         return;
        //     }
        //     let node = cc.instantiate(prefab);
        //     node.parent = Global.instance.popNode;
        //     node.position = new cc.Vec3(0, 0);
        //     node.getComponent(AllGameView).gameDataList(currentType, () => {

        //     });
        //     setTimeout(() => {
        //         cc.director.emit("Multi_Toggle_Provider_Result", c_provider);
        //     }, 1000);
        // });
    }

    //跳转到allgames 并且塞选 '10004'  ['1','3']
    showAllGames_gtype_providers(gtype?, providers?) {
        let gametype = "Casino";
        if (parseInt(gtype) == GlobalEnum.GAME_TYPE.CASINO) {
            gametype = "Casino";
        } else if (parseInt(gtype) == GlobalEnum.GAME_TYPE.SLOTS) {
            gametype = "Slots";
        } else if (parseInt(gtype) == GlobalEnum.GAME_TYPE.POKER) {
            gametype = "Poker";
        } else if (parseInt(gtype) == GlobalEnum.GAME_TYPE.BINGO) {
            gametype = "Bingo";
        } else if (parseInt(gtype) == GlobalEnum.GAME_TYPE.FISHING) {
            gametype = "Arcade";
        } else if (parseInt(gtype) == GlobalEnum.GAME_TYPE.SPORTS) {
            gametype = "Sports";
        }
        let arr_provider = providers;
        let provider_name;
        const provider_name_list = new Set();//set自动去重
        if (arr_provider && arr_provider.length > 0) {
            for (let i = 0; i < arr_provider.length; i++) {
                provider_name = Global.getInstance().getGameProviderNameById(parseInt(arr_provider[i]))
                if (provider_name.length > 0) {
                    provider_name_list.add(provider_name);
                }
            }
        }
        if (!Global.getInstance().token) {
            //未登录 跳转登录页
            uiManager.instance.showDialog(UI_PATH_DIC.phonePasswordLogin, [{ way: LOGIN_WAY.PhoneCode, curPromoPage: false }])
            return;
        }
        const array_providers = Array.from(provider_name_list);
        uiManager.instance.loadPrefabByLoading(UI_PATH_DIC.AllGameView, cc.Prefab, () => {
        }, (err, prefab: any) => {
            if (err) {
                return;
            }
            let node = cc.instantiate(prefab);
            node.parent = Global.instance.popNode;
            node.position = new cc.Vec3(0, 0);
            node.getComponent(AllGameView).gameDataList(gametype, () => {

            });
            setTimeout(() => {
                cc.director.emit("Multi_Toggle_Provider_Result", array_providers);
            }, 1000);
        });
    }
}
