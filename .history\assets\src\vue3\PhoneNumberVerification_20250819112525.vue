<template>
  <div v-if="visible" class="phone-verification-overlay">
    <div class="verification-dialog">
      <div class="dialog-header">
        <h3>{{ currentTitle }}</h3>
        <button @click="close" class="close-btn">×</button>
      </div>

      <div class="dialog-content">
        <!-- Phone Number Input Step -->
        <div v-if="currentStep === 'phone'" class="phone-input-step">
          <div class="input-group">
            <label>{{ $t("phone_number") }}</label>
            <div class="input-wrapper">
              <input
                v-model="phoneNumber"
                type="tel"
                :placeholder="$t('enter_phone_number')"
                class="phone-input"
                :class="{ error: phoneError }"
                @input="clearPhoneError"
                @focus="onInputFocus"
                @blur="onInputBlur"
              />
              <div v-if="phoneError" class="error-line"></div>
            </div>
            <div v-if="phoneError" class="error-text">{{ phoneErrorMessage }}</div>
          </div>

          <button @click="verifyPhone" class="primary-btn" :disabled="!phoneNumber || phoneError">
            {{ $t("next") }}
          </button>
        </div>

        <!-- Verification Code Step -->
        <div v-if="currentStep === 'code'" class="code-input-step">
          <div class="phone-display">
            <span>{{ $t("verification_code_sent_to") }}</span>
            <strong>{{ formatPhoneNumber(phoneNumber) }}</strong>
          </div>

          <div class="input-group">
            <label>{{ $t("verification_code") }}</label>
            <div class="input-wrapper">
              <input
                v-model="verificationCode"
                type="text"
                :placeholder="$t('enter_6_digit_code')"
                class="code-input"
                :class="{ error: codeError }"
                maxlength="6"
                @input="clearCodeError"
                @focus="onInputFocus"
                @blur="onInputBlur"
              />
              <div v-if="codeError" class="error-line"></div>
            </div>
            <div v-if="codeError" class="error-text">{{ codeErrorMessage }}</div>
          </div>

          <div class="code-actions">
            <button v-if="!isCountingDown" @click="sendVerificationCode" class="secondary-btn" :disabled="isSending">
              {{ isSending ? $t("sending") : $t("resend_code") }}
            </button>
            <div v-else class="countdown">{{ $t("resend_in") }} {{ countdown }}s</div>
          </div>

          <button @click="confirmVerification" class="primary-btn" :disabled="!verificationCode || verificationCode.length !== 6 || isSubmitting">
            {{ isSubmitting ? $t("submitting") : $t("done") }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted, watch } from "vue";
import { useI18n } from "vue-i18n";
import { useGlobalStore } from "@/stores/global";
import { CloudFlareScene } from "./CloudflareController.vue";

// Types
export enum VerifyType {
  SetPhoneNumber = 0,
  ForgetPassword = 1,
  ChangePhoneNumber = 2,
  AddWithdrawAccount = 3,
  ChangeWithdrawAccount = 4,
}

export enum SmsType {
  BIND_PHONE = 1,
  UPDATE_LOGIN_PASSWORD = 2,
  UPDATE_PHONE = 3,
  BIND_WITHDRAW_ACCOUNT = 4,
  UPDATE_WITHDRAW_ACCOUNT = 5,
}

interface Props {
  verifyType: VerifyType;
  initialPhone?: string;
  onSuccess?: (data: any) => void;
  onCancel?: () => void;
}

const props = withDefaults(defineProps<Props>(), {
  verifyType: VerifyType.SetPhoneNumber,
  initialPhone: "",
  onSuccess: () => {},
  onCancel: () => {},
});

// Composables
const { t } = useI18n();
const globalStore = useGlobalStore();

// Reactive state
const visible = ref(false);
const currentStep = ref<"phone" | "code">("phone");
const phoneNumber = ref("");
const verificationCode = ref("");
const phoneError = ref(false);
const phoneErrorMessage = ref("");
const codeError = ref(false);
const codeErrorMessage = ref("");
const isSending = ref(false);
const isSubmitting = ref(false);
const isCountingDown = ref(false);
const countdown = ref(60);
const countdownTimer = ref<NodeJS.Timeout | null>(null);
const smsType = ref(SmsType.BIND_PHONE);

// Computed
const currentTitle = computed(() => {
  const titles = {
    [VerifyType.SetPhoneNumber]: t("set_phone_number"),
    [VerifyType.ForgetPassword]: t("change_login_password"),
    [VerifyType.ChangePhoneNumber]: t("change_phone_number"),
    [VerifyType.AddWithdrawAccount]: t("add_fund_account"),
    [VerifyType.ChangeWithdrawAccount]: t("change_fund_account"),
  };
  return titles[props.verifyType] || t("phone_verification");
});

// Methods
const show = () => {
  visible.value = true;
  initializeComponent();
};

const close = () => {
  visible.value = false;
  currentStep.value = "phone";
  phoneNumber.value = "";
  verificationCode.value = "";
  clearErrors();
  stopCountdown();
  props.onCancel?.();
};

const initializeComponent = () => {
  // Initialize based on verify type
  if (props.verifyType === VerifyType.SetPhoneNumber) {
    const userData = globalStore.userdata;
    if (userData?.phone) {
      phoneNumber.value = userData.phone;
    }
  } else if (props.verifyType === VerifyType.ForgetPassword) {
    const phone = props.initialPhone || globalStore.getStorageData("PHONE", "");
    if (phone) {
      phoneNumber.value = phone;
      currentStep.value = "code";
    }
  } else if (props.verifyType === VerifyType.ChangePhoneNumber) {
    // For change phone number, start with phone input
    currentStep.value = "phone";
  } else if ([VerifyType.AddWithdrawAccount, VerifyType.ChangeWithdrawAccount].includes(props.verifyType)) {
    const userData = globalStore.userdata;
    if (userData?.phone) {
      phoneNumber.value = userData.phone;
      currentStep.value = "code";
    }
  }

  // Check if countdown is active
  checkCountdown();
};

const formatPhoneNumber = (phone: string) => {
  if (!phone || phone.length < 6) return phone;
  return `${phone.slice(0, 2)}****${phone.slice(-4)}`;
};

const isValidPhilippinePhone = (phone: string) => {
  // Philippine phone number validation
  const phoneRegex = /^(09|\+639)\d{9}$/;
  return phoneRegex.test(phone.replace(/\s+/g, ""));
};

const clearPhoneError = () => {
  phoneError.value = false;
  phoneErrorMessage.value = "";
};

const clearCodeError = () => {
  codeError.value = false;
  codeErrorMessage.value = "";
};

const clearErrors = () => {
  clearPhoneError();
  clearCodeError();
};

const onInputFocus = () => {
  // Handle input focus for mobile keyboard
  if (globalStore.needScreenUp()) {
    // Adjust UI for mobile keyboard
    document.body.style.transform = "translateY(-100px)";
  }
};

const onInputBlur = () => {
  // Reset UI when input loses focus
  document.body.style.transform = "";
};

const verifyPhone = () => {
  const trimmedPhone = phoneNumber.value.trim();

  if (!trimmedPhone) {
    phoneError.value = true;
    phoneErrorMessage.value = t("phone_number_cannot_be_empty");
    return;
  }

  if (!isValidPhilippinePhone(trimmedPhone)) {
    phoneError.value = true;
    phoneErrorMessage.value = t("wrong_phone_number");
    return;
  }

  phoneNumber.value = trimmedPhone;
  clearPhoneError();
  currentStep.value = "code";
};

const sendVerificationCode = async () => {
  if (isSending.value || isCountingDown.value) return;

  isSending.value = true;

  try {
    // Determine verification type and scene
    let scene: CloudFlareScene;
    let smsTypeValue: SmsType;

    switch (props.verifyType) {
      case VerifyType.SetPhoneNumber:
        scene = CloudFlareScene.BIND_PHONE_GET_CODE;
        smsTypeValue = SmsType.BIND_PHONE;
        break;
      case VerifyType.ChangePhoneNumber:
        scene = CloudFlareScene.MODIFY_PHONE_GET_CODE;
        smsTypeValue = SmsType.UPDATE_PHONE;
        break;
      case VerifyType.AddWithdrawAccount:
        smsTypeValue = SmsType.BIND_WITHDRAW_ACCOUNT;
        break;
      case VerifyType.ChangeWithdrawAccount:
        smsTypeValue = SmsType.UPDATE_WITHDRAW_ACCOUNT;
        break;
      case VerifyType.ForgetPassword:
        scene = CloudFlareScene.FORGET_PW_GET_CODE;
        smsTypeValue = SmsType.UPDATE_LOGIN_PASSWORD;
        break;
      default:
        throw new Error("Invalid verify type");
    }

    smsType.value = smsTypeValue;

    // Get verification token if needed
    let verifyToken = "";
    if (globalStore.loginVerifyType === 2 && scene) {
      // Use Cloudflare verification
      const cloudflareCtrl = globalStore.getCloudflareController();
      const result = await cloudflareCtrl.getVerifyToken(scene);
      if (result.code !== 0 || !result.token) {
        throw new Error("Cloudflare verification failed");
      }
      verifyToken = result.token;
    }

    // Send SMS
    const params = {
      phone: phoneNumber.value,
      telephoneCode: "+63",
      type: smsTypeValue,
      buds: "64",
    };

    if (globalStore.loginVerifyType === 2 && verifyToken) {
      params["cf-token"] = verifyToken;
      params["cf-scene"] = scene;
    }

    await globalStore.sendSms(params);

    globalStore.showTip(t("verification_code_sent_successfully"));
    startCountdown();
  } catch (error) {
    console.error("Send verification code error:", error);
    globalStore.showTip(error.message || t("send_code_failed"));
  } finally {
    isSending.value = false;
  }
};

const confirmVerification = async () => {
  if (isSubmitting.value) return;

  const code = verificationCode.value.trim();
  if (code.length !== 6) {
    codeError.value = true;
    codeErrorMessage.value = t("code_error_please_try_again");
    return;
  }

  clearCodeError();
  isSubmitting.value = true;

  try {
    let result;

    if (props.verifyType === VerifyType.ForgetPassword) {
      result = await handleForgetPassword(code);
    } else if (props.verifyType === VerifyType.SetPhoneNumber) {
      result = await handleBindPhone(code);
    } else if (props.verifyType === VerifyType.ChangePhoneNumber) {
      result = await handleChangePhone(code);
    } else if ([VerifyType.AddWithdrawAccount, VerifyType.ChangeWithdrawAccount].includes(props.verifyType)) {
      result = await handleWithdrawAccount(code);
    }

    if (result) {
      globalStore.showTip(t("verification_successful"));
      props.onSuccess?.(result);
      close();
    }
  } catch (error) {
    console.error("Verification error:", error);
    codeError.value = true;
    codeErrorMessage.value = error.message || t("verification_error_please_try_again");
  } finally {
    isSubmitting.value = false;
  }
};

const handleForgetPassword = async (code: string) => {
  // Verify SMS code first
  await globalStore.verifySmsCode({
    phone: phoneNumber.value,
    telephoneCode: "+63",
    code,
    type: smsType.value,
  });

  return { phone: phoneNumber.value, code };
};

const handleBindPhone = async (code: string) => {
  // Get verification token if needed
  let verifyToken = "";
  if (globalStore.loginVerifyType === 2) {
    const cloudflareCtrl = globalStore.getCloudflareController();
    const result = await cloudflareCtrl.getVerifyToken(CloudFlareScene.BIND_PHONE_SUBMIT);
    if (result.code !== 0 || !result.token) {
      throw new Error("Cloudflare verification failed");
    }
    verifyToken = result.token;
  }

  const params = {
    phone: phoneNumber.value,
    verifyCode: code,
    telephoneCode: "+63",
    buds: "64",
  };

  if (globalStore.loginVerifyType === 2 && verifyToken) {
    params["cf-token"] = verifyToken;
    params["cf-scene"] = CloudFlareScene.BIND_PHONE_SUBMIT;
  }

  return await globalStore.bindPhone(params);
};

const handleChangePhone = async (code: string) => {
  // Get verification token if needed
  let verifyToken = "";
  if (globalStore.loginVerifyType === 2) {
    const cloudflareCtrl = globalStore.getCloudflareController();
    const result = await cloudflareCtrl.getVerifyToken(CloudFlareScene.MODIFY_PHONE_SUBMIT);
    if (result.code !== 0 || !result.token) {
      throw new Error("Cloudflare verification failed");
    }
    verifyToken = result.token;
  }

  const params = {
    old_phone: props.initialPhone || globalStore.userdata?.phone,
    phone: phoneNumber.value,
    verifyCode: code,
    telephoneCode: "+63",
    buds: "64",
  };

  if (globalStore.loginVerifyType === 2 && verifyToken) {
    params["cf-token"] = verifyToken;
    params["cf-scene"] = CloudFlareScene.MODIFY_PHONE_SUBMIT;
  }

  return await globalStore.changePhone(params);
};

const handleWithdrawAccount = async (code: string) => {
  // Verify SMS code
  await globalStore.verifySmsCode({
    phone: phoneNumber.value,
    telephoneCode: "+63",
    code,
    type: smsType.value,
  });

  return { verified: true };
};

const startCountdown = () => {
  isCountingDown.value = true;
  countdown.value = 60;

  const endTime = Date.now() + 60 * 1000;
  globalStore.setStorageData("COUNTDOWN_ENDTIME", endTime);

  countdownTimer.value = setInterval(() => {
    const currentTime = Date.now();
    const remainingTime = Math.max(0, endTime - currentTime);
    countdown.value = Math.ceil(remainingTime / 1000);

    if (remainingTime <= 0) {
      stopCountdown();
    }
  }, 1000);
};

const stopCountdown = () => {
  isCountingDown.value = false;
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value);
    countdownTimer.value = null;
  }
  globalStore.setStorageData("COUNTDOWN_ENDTIME", Date.now());
};

const checkCountdown = () => {
  const endTime = globalStore.getStorageData("COUNTDOWN_ENDTIME", 0);
  const currentTime = Date.now();
  const remainingTime = Math.max(0, endTime - currentTime);

  if (remainingTime > 0) {
    isCountingDown.value = true;
    countdown.value = Math.ceil(remainingTime / 1000);

    countdownTimer.value = setInterval(() => {
      const currentTime = Date.now();
      const remainingTime = Math.max(0, endTime - currentTime);
      countdown.value = Math.ceil(remainingTime / 1000);

      if (remainingTime <= 0) {
        stopCountdown();
      }
    }, 1000);
  }
};

// Lifecycle
onMounted(() => {
  // Auto-show if needed
  if (props.verifyType !== undefined) {
    show();
  }
});

onUnmounted(() => {
  stopCountdown();
  document.body.style.transform = "";
});

// Watch for prop changes
watch(
  () => props.verifyType,
  () => {
    if (visible.value) {
      initializeComponent();
    }
  }
);

// Expose methods
defineExpose({
  show,
  close,
});
</script>

<style scoped>
.phone-verification-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.verification-dialog {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 400px;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #eee;
  background: #f8f9fa;
}

.dialog-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s;
}

.close-btn:hover {
  background-color: #f0f0f0;
  color: #333;
}

.dialog-content {
  padding: 24px;
}

.phone-input-step,
.code-input-step {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.phone-display {
  text-align: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 8px;
}

.phone-display span {
  color: #666;
  font-size: 14px;
}

.phone-display strong {
  color: #333;
  font-size: 16px;
  margin-left: 8px;
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.input-group label {
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.input-wrapper {
  position: relative;
}

.phone-input,
.code-input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 16px;
  transition: all 0.2s;
  box-sizing: border-box;
}

.phone-input:focus,
.code-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.phone-input.error,
.code-input.error {
  border-color: #dc3545;
}

.error-line {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background-color: #dc3545;
  border-radius: 0 0 8px 8px;
}

.error-text {
  color: #dc3545;
  font-size: 12px;
  margin-top: 4px;
}

.code-actions {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 40px;
}

.countdown {
  color: #666;
  font-size: 14px;
}

.primary-btn {
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
  border: none;
  padding: 14px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  width: 100%;
}

.primary-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #0056b3, #004085);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

.primary-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.secondary-btn {
  background: transparent;
  color: #007bff;
  border: 2px solid #007bff;
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.secondary-btn:hover:not(:disabled) {
  background: #007bff;
  color: white;
}

.secondary-btn:disabled {
  border-color: #ccc;
  color: #ccc;
  cursor: not-allowed;
}

/* Mobile responsive */
@media (max-width: 480px) {
  .verification-dialog {
    width: 95%;
    margin: 20px;
  }

  .dialog-content {
    padding: 20px;
  }

  .dialog-header {
    padding: 16px 20px;
  }

  .phone-input,
  .code-input {
    font-size: 16px; /* Prevent zoom on iOS */
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .verification-dialog {
    background: #2d3748;
    color: #e2e8f0;
  }

  .dialog-header {
    background: #4a5568;
    border-bottom-color: #4a5568;
  }

  .dialog-header h3 {
    color: #e2e8f0;
  }

  .close-btn {
    color: #a0aec0;
  }

  .close-btn:hover {
    background-color: #4a5568;
    color: #e2e8f0;
  }

  .phone-display {
    background: #4a5568;
  }

  .phone-display span {
    color: #a0aec0;
  }

  .phone-display strong {
    color: #e2e8f0;
  }

  .input-group label {
    color: #e2e8f0;
  }

  .phone-input,
  .code-input {
    background: #4a5568;
    border-color: #718096;
    color: #e2e8f0;
  }

  .phone-input:focus,
  .code-input:focus {
    border-color: #63b3ed;
    box-shadow: 0 0 0 3px rgba(99, 179, 237, 0.1);
  }

  .countdown {
    color: #a0aec0;
  }
}
</style>
