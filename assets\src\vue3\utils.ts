/**
 * Vue3 Phone Verification Utils
 * 从 Cocos Creator 代码转换的工具函数
 */

import { PHONE_REGEX, VERIFICATION_CODE_LENGTH } from './types'

/**
 * 手机号格式化 - 隐藏中间部分
 * @param phoneNumber 手机号
 * @returns 格式化后的手机号 (例: 09****6789)
 */
export function formatPhoneNumber(phoneNumber: string): string {
  if (!phoneNumber || phoneNumber.length < 6) {
    return phoneNumber
  }
  return `${phoneNumber.slice(0, 2)}****${phoneNumber.slice(-4)}`
}

/**
 * 验证菲律宾手机号格式
 * @param phone 手机号
 * @returns 是否为有效的菲律宾手机号
 */
export function isValidPhilippinePhone(phone: string): boolean {
  if (!phone) return false
  const cleanPhone = phone.replace(/\s+/g, '')
  return PHONE_REGEX.test(cleanPhone)
}

/**
 * 验证验证码格式
 * @param code 验证码
 * @returns 是否为有效的验证码
 */
export function isValidVerificationCode(code: string): boolean {
  if (!code) return false
  const cleanCode = code.trim()
  return cleanCode.length === VERIFICATION_CODE_LENGTH && /^\d+$/.test(cleanCode)
}

/**
 * 清理字符串 - 去除首尾空格
 * @param str 输入字符串
 * @returns 清理后的字符串
 */
export function stringTrim(str: string): string {
  return str ? str.trim() : ''
}

/**
 * 获取操作系统类型
 * @returns 操作系统类型
 */
export function getOperatingSystem(): string {
  const platform = navigator.platform.toLowerCase()
  if (platform.includes('win')) return 'windows'
  if (platform.includes('mac')) return 'macos'
  if (platform.includes('linux')) return 'linux'
  if (platform.includes('iphone') || platform.includes('ipad')) return 'ios'
  if (platform.includes('android')) return 'android'
  return 'unknown'
}

/**
 * 检查是否为移动设备
 * @returns 是否为移动设备
 */
export function isMobileDevice(): boolean {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
}

/**
 * 生成随机字符串
 * @param length 长度
 * @returns 随机字符串
 */
export function generateRandomString(length: number = 8): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  let result = ''
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

/**
 * 延迟执行
 * @param ms 延迟毫秒数
 * @returns Promise
 */
export function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms))
}

/**
 * 防抖函数
 * @param func 要防抖的函数
 * @param wait 等待时间
 * @returns 防抖后的函数
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null
  
  return function executedFunction(...args: Parameters<T>) {
    const later = () => {
      timeout = null
      func(...args)
    }
    
    if (timeout) {
      clearTimeout(timeout)
    }
    timeout = setTimeout(later, wait)
  }
}

/**
 * 节流函数
 * @param func 要节流的函数
 * @param limit 限制时间
 * @returns 节流后的函数
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean = false
  
  return function executedFunction(...args: Parameters<T>) {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

/**
 * 深拷贝对象
 * @param obj 要拷贝的对象
 * @returns 拷贝后的对象
 */
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') {
    return obj
  }
  
  if (obj instanceof Date) {
    return new Date(obj.getTime()) as unknown as T
  }
  
  if (obj instanceof Array) {
    return obj.map(item => deepClone(item)) as unknown as T
  }
  
  if (typeof obj === 'object') {
    const clonedObj = {} as T
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key])
      }
    }
    return clonedObj
  }
  
  return obj
}

/**
 * 格式化时间为 MM:SS 格式
 * @param seconds 秒数
 * @returns 格式化后的时间字符串
 */
export function formatTime(seconds: number): string {
  const mins = Math.floor(seconds / 60)
  const secs = seconds % 60
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

/**
 * 获取倒计时剩余时间
 * @param endTime 结束时间戳
 * @returns 剩余秒数
 */
export function getRemainingTime(endTime: number): number {
  const currentTime = Date.now()
  return Math.max(0, Math.ceil((endTime - currentTime) / 1000))
}

/**
 * 本地存储工具类
 */
export class LocalStorage {
  /**
   * 设置本地存储
   * @param key 键
   * @param value 值
   */
  static setItem(key: string, value: any): void {
    try {
      const serializedValue = JSON.stringify(value)
      localStorage.setItem(key, serializedValue)
    } catch (error) {
      console.error('LocalStorage setItem error:', error)
    }
  }

  /**
   * 获取本地存储
   * @param key 键
   * @param defaultValue 默认值
   * @returns 存储的值
   */
  static getItem<T>(key: string, defaultValue: T): T {
    try {
      const item = localStorage.getItem(key)
      if (item === null) {
        return defaultValue
      }
      return JSON.parse(item)
    } catch (error) {
      console.error('LocalStorage getItem error:', error)
      return defaultValue
    }
  }

  /**
   * 移除本地存储
   * @param key 键
   */
  static removeItem(key: string): void {
    try {
      localStorage.removeItem(key)
    } catch (error) {
      console.error('LocalStorage removeItem error:', error)
    }
  }

  /**
   * 清空本地存储
   */
  static clear(): void {
    try {
      localStorage.clear()
    } catch (error) {
      console.error('LocalStorage clear error:', error)
    }
  }
}

/**
 * URL 参数工具类
 */
export class URLParams {
  /**
   * 构建 URL 查询字符串
   * @param params 参数对象
   * @returns 查询字符串
   */
  static buildQuery(params: Record<string, any>): string {
    const searchParams = new URLSearchParams()
    
    Object.keys(params).forEach(key => {
      const value = params[key]
      if (value !== null && value !== undefined) {
        searchParams.append(key, String(value))
      }
    })
    
    return searchParams.toString()
  }

  /**
   * 解析 URL 查询字符串
   * @param query 查询字符串
   * @returns 参数对象
   */
  static parseQuery(query: string): Record<string, string> {
    const params: Record<string, string> = {}
    const searchParams = new URLSearchParams(query)
    
    searchParams.forEach((value, key) => {
      params[key] = value
    })
    
    return params
  }
}

/**
 * 错误处理工具类
 */
export class ErrorHandler {
  /**
   * 处理 API 错误
   * @param error 错误对象
   * @returns 格式化的错误信息
   */
  static handleApiError(error: any): string {
    if (error?.response?.data?.message) {
      return error.response.data.message
    }
    
    if (error?.message) {
      return error.message
    }
    
    if (typeof error === 'string') {
      return error
    }
    
    return 'Unknown error occurred'
  }

  /**
   * 记录错误日志
   * @param error 错误对象
   * @param context 上下文信息
   */
  static logError(error: any, context?: string): void {
    const errorInfo = {
      message: this.handleApiError(error),
      context,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    }
    
    console.error('Error logged:', errorInfo)
    
    // 这里可以添加错误上报逻辑
    // 例如发送到错误监控服务
  }
}

/**
 * 表单验证工具类
 */
export class FormValidator {
  /**
   * 验证必填字段
   * @param value 值
   * @param fieldName 字段名
   * @returns 验证结果
   */
  static required(value: any, fieldName: string): { valid: boolean; message: string } {
    const isValid = value !== null && value !== undefined && String(value).trim() !== ''
    return {
      valid: isValid,
      message: isValid ? '' : `${fieldName} is required`
    }
  }

  /**
   * 验证手机号
   * @param phone 手机号
   * @returns 验证结果
   */
  static validatePhone(phone: string): { valid: boolean; message: string } {
    if (!phone) {
      return { valid: false, message: 'Phone number is required' }
    }
    
    const isValid = isValidPhilippinePhone(phone)
    return {
      valid: isValid,
      message: isValid ? '' : 'Invalid phone number format'
    }
  }

  /**
   * 验证验证码
   * @param code 验证码
   * @returns 验证结果
   */
  static validateCode(code: string): { valid: boolean; message: string } {
    if (!code) {
      return { valid: false, message: 'Verification code is required' }
    }
    
    const isValid = isValidVerificationCode(code)
    return {
      valid: isValid,
      message: isValid ? '' : 'Invalid verification code format'
    }
  }
}

/**
 * 设备信息工具类
 */
export class DeviceInfo {
  /**
   * 获取设备ID（简单实现）
   * @returns 设备ID
   */
  static getDeviceId(): string {
    let deviceId = LocalStorage.getItem('device_id', '')
    
    if (!deviceId) {
      deviceId = generateRandomString(16)
      LocalStorage.setItem('device_id', deviceId)
    }
    
    return deviceId
  }

  /**
   * 获取应用版本
   * @returns 应用版本
   */
  static getAppVersion(): string {
    return process.env.VUE_APP_VERSION || '1.0.0'
  }

  /**
   * 获取应用包名
   * @returns 应用包名
   */
  static getAppBundleId(): string {
    return process.env.VUE_APP_BUNDLE_ID || 'com.example.app'
  }

  /**
   * 检查是否需要屏幕上移（移动端键盘弹出）
   * @returns 是否需要上移
   */
  static needScreenUp(): boolean {
    return isMobileDevice() && window.innerHeight < 600
  }
}
