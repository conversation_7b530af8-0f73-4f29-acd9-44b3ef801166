let deferredPrompt = null;
const isIOS = /iphone|ipad|ipod/i.test(navigator.userAgent);
const isStandalone = ('standalone' in navigator) && navigator.standalone;
const hasInstalled = localStorage.getItem('pwa_installed');

// 监听支持的浏览器
if ('onbeforeinstallprompt' in window) {
    console.log('[PWA] onbeforeinstallprompt');
    window.addEventListener('beforeinstallprompt', (e) => {
        console.log('[PWA] beforeinstallprompt');
        e.preventDefault();
        deferredPrompt = e;
        localStorage.removeItem('pwa_installed'); // 允许重新安装
    });

    window.addEventListener("appinstalled", () => {
        console.log('[PWA] appinstalled');
        localStorage.setItem('pwa_installed', 'true');
    })
}

window.pwaInstall = async () => {
    console.log('[PWA] pwaInstall');
    if ('onbeforeinstallprompt' in window) {
        if (deferredPrompt) {
            console.log('[PWA] deferredPrompt');
            deferredPrompt.prompt();
            const choice = await deferredPrompt.userChoice;
            console.log('安装结果:', choice.outcome);
            if (choice.outcome === 'accepted') {
                localStorage.setItem('pwa_installed', 'true');
            }
            deferredPrompt = null;
        }
        else {
            console.log('[PWA] 冷却期或策略未触发 → 手动引导');
            if (!isStandalone && !hasInstalled) {
                if (isIOS) {
                    showIOSGuide();
                } else {
                    showManualGuide();
                }
            }
        }
    }
    else {
        console.log('[PWA] 冷却期或策略未触发 → 手动引导');
        if (!isStandalone && !hasInstalled) {
            if (isIOS) {
                showIOSGuide();
            } else {
                showManualGuide();
            }
        }
    }
}

function showManualGuide() {
    console.log('[PWA] android引导');

    if (document.getElementById("showManualGuide")) return;
    const guide = document.createElement('div');
    guide.id = "showManualGuide";

    guide.innerHTML = `
      <div style="
        position: fixed; bottom: 0; left: 0; right: 0;
        background: rgba(0,0,0,0.8); color: white;
        padding: 15px; font-size: 14px; text-align: center; z-index: 9999;
      ">
        Open the menu and select "Add to Home Screen".
        <span style="margin-left:10px;cursor:pointer;color:#ff9800;" id="pwa-close">close</span>
      </div>
    `;
    document.body.appendChild(guide);
    document.getElementById('pwa-close').addEventListener('click', () => {
        guide.remove();
    });
}

function showIOSGuide() {
    console.log('[PWA] ios引导');

    if (document.getElementById("showIOSGuide")) return;
    const guide = document.createElement('div');
    guide.id = "showIOSGuide";

    guide.tagName = "showIOSGuide";
    guide.innerHTML = `
      <div style="
        position: fixed; bottom: 0; left: 0; right: 0;
        background: rgba(0,0,0,0.8); color: white;
        padding: 15px; font-size: 14px; text-align: center; z-index: 9999;
      ">
        Tap Safari's bottom button, then select "Add to Home Screen".
        <span style="margin-left:10px;cursor:pointer;color:#ff9800;" id="pwa-close">close</span>
      </div>
    `;
    document.body.appendChild(guide);
    document.getElementById('pwa-close').addEventListener('click', () => {
        guide.remove();
    });
}