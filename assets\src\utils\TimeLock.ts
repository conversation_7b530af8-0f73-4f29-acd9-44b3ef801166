/**
 * 定时锁，提供 xx 毫秒后自动解锁的功能
 */
export default class TimeLock {
    /** 解锁计时器ID */
    private _unlockTimerId = 0;
  
    /** 锁锁定时长(毫秒) */
    private _lockDuration = 15000;
    public get lockDuration() {
        return this._lockDuration;
    }
    public set lockDuration(value: number) {
        this._lockDuration = value;
    }
  
    /** 锁定状态 */
    private _lock = false;
    public get lock() {
      return this._lock;
    }
    set lock(value: boolean) {
      this._lock = value;
      if (this._unlockTimerId) {
        // 停止现有计时器
        clearTimeout(this._unlockTimerId);
        this._unlockTimerId = 0;
      }
      if (value) {
        // 添加新的计时器
        this._unlockTimerId = setTimeout(() => {
          this._lock = false;
          this._unlockTimerId = 0;
        }, this._lockDuration);
      }
    }

    /** 构造函数 */
    constructor(lockDuration = 15000) {
        this._lockDuration = lockDuration;
    }
  }
  