import { Md5 } from './../libs/Md5';
import { ALL_APP_SOURCE_CONFIG } from "../Config";
import GameControl from "../GameControl";
import { DEEP_INDEXZ, E_CHANEL_TYPE, E_GAME_TYPE, E_SCENE_TYPE, LOCK_ACCOUNT, MAINTENANCE_GAME, MAINTENANCETIPCODE, MAN_MADE_LOGIN, TOKENEXPIRE_CODE, UI_PATH_DIC } from "../GlobalConstant";
import Global from "../GlobalScript";
// import { ALL_APP_SOURCE_CONFIG } from "../Config";
import GoogleSignInUtils from "../GoogleSIgnInUtils";
import Hall from "../hall/Hall";
import { showMoreGame } from "../MoreGame";
import { showMoreGameList } from "../MoreGameList";
import HttpUtils from "../net/HttpUtils";
import { SocketUtils } from "../net/SocketUtils";
import CaptchaManager from "./CaptchaManager";
import LoginHelper from "./LoginHelper";
// import { showCustomerService } from "../CustomerService";
import { uiManager } from '../mgr/UIManager';
import WebContentUI from '../WebContentUI';
import MoreGameManager from '../MoreGameManager';
import ZendeskManager from '../ZendeskManager';
import { CLIENTLOGKEY, ClientLogManager } from '../ClientLogConfis';
import { LOGIN_WAY, showPhonePasswordLogin } from './phonePasswordLogin';
import { showMaintenancetip } from '../Maintenancetip';
import BackgroundAdapter, { FIT_POLICY } from '../component/BackgroundAdapter';
import { GeetestMgr } from '../geetest/GeetestMgr';
import { SERVICE_TYPE, serviceMgr } from '../mgr/serviceMgr';
import { util } from 'protobufjs';
import utils from '../utils/utils';
import { LoginMgr } from './LoginMgr';
import HttpProxy from '../net/HttpProxy';
const { ccclass, property } = cc._decorator;
cc.macro.ENABLE_TRANSPARENT_CANVAS = true;

export enum ELOGIN_TYPE {
    LOGIN_PHONE = 0,
    LOGIN_PASSWORD = 1,
    LOGIN_FACEBOOK = 2,
    LOGIN_GOOGLE = 3,
    LOGIN_GUEST
}
@ccclass
export default class Login extends cc.Component {
    @property(cc.Node)
    midiNode: cc.Node = null;

    @property(cc.Label)
    loadtip: cc.Label = null;

    @property(cc.Node)
    updatenode: cc.Node = null;

    // @property(cc.Node)
    // updatebg: cc.Node = null;

    @property(cc.Node)
    updatetip: cc.Node = null;

    @property(cc.Node)
    btnLay: cc.Node = null;

    manifestUrl: cc.Asset = null;

    @property(cc.Prefab)
    loading_prefab: cc.Prefab = null;

    @property(cc.Label)
    version_label: cc.Label = null;

    @property([cc.SpriteFrame])
    nationalflag: cc.SpriteFrame[] = [];

    @property(cc.Label)
    freeCount: cc.Label = null;

    @property(cc.Node)
    ndCheckTips: cc.Node = null

    @property(BackgroundAdapter)
    loginBgAdapter: BackgroundAdapter = null

    // @property(cc.SpriteFrame)
    // logo_frame:cc.SpriteFrame = null;

    // @property([cc.Sprite])
    // country: cc.Sprite[] = [];

    // @property(cc.Sprite)
    // currency: cc.Sprite = null;

    // @property(cc.SpriteFrame)
    // normalcurrency: cc.SpriteFrame = null;
    _autologin_times = 0;//自动登录尝试次数 防止多次登录问题
    _updating: boolean = false;
    // _canRetry: boolean = false;
    _storagePath: any = null;
    _versionCompareHandle: any = null;
    _am: any = null;
    _failCount: any = 0;
    _loginType: any = null;
    _loadBeginTime = 0
    // LIFE-CYCLE CALLBACKS:
    onLoad() {
        Global.getInstance().popNode = this.node.getChildByName("popNode")
        Global.getInstance().popNode.zIndex = 999
        Global.getInstance().setSceneId(E_SCENE_TYPE.LOGIN);
        if (cc.winSize.height / cc.winSize.width < 16 / 9) {
            this.node.getComponent(cc.Canvas).fitHeight = true;
            this.node.getComponent(cc.Canvas).fitWidth = false;
        } else {
            this.node.getComponent(cc.Canvas).fitHeight = false;
            this.node.getComponent(cc.Canvas).fitWidth = true;
        }
        this._loadBeginTime = Global.getInstance().now()
        // ClientLogManager.addLogs(CLIENTLOGKEY.GameOpenToLogin, { title: "GameOpenToLogin begin" })
        // window.addEventListener('orientationchange', this.oriChange.bind(this));
        this.preLoadPrefab()
        cc.director.on("AutoLoginEvent", this.onEventMaintenanceTry, this)
        if (cc.sys.isBrowser) {
            // let fullLoginMode = utils.getBrowserValue("full_login_mode");
            // if (fullLoginMode) {
            this.loginShow();
            // } else {
            //     this.hideLogin()
            // }
            this.change_debug_mode()
            let gcash_auth_code = utils.getBrowserValue("gcash_auth_code");
            let g_auth_code = utils.getBrowserValue("g_auth_code");
            let w_auth_code = utils.getBrowserValue("w_auth_code");
            if (gcash_auth_code || g_auth_code || ALL_APP_SOURCE_CONFIG.channel == E_CHANEL_TYPE.G_CASH) {
                Global.getInstance().gcashMode = true;
                ALL_APP_SOURCE_CONFIG.channel = E_CHANEL_TYPE.G_CASH
            }
            if (w_auth_code) {
                ALL_APP_SOURCE_CONFIG.channel = E_CHANEL_TYPE.WEB

            }
            let maya_sesstionid = utils.getBrowserValue("sessionId");
            if (maya_sesstionid || ALL_APP_SOURCE_CONFIG.channel == E_CHANEL_TYPE.MAYA) {
                Global.getInstance().mayaMode = true;
                ALL_APP_SOURCE_CONFIG.channel = E_CHANEL_TYPE.MAYA
            }
            this.getConfig(() => {
                this.autoLogin(true)
                //更新登陆方式受服务器控制
                const show_fb = Global.getInstance().getLoginFBStatus()
                const show_google = Global.getInstance().getLoginGoogleStatus()
                if (show_fb) LoginMgr.instance.facebook_init();
                if (show_google) LoginMgr.instance.google_init();
            })
            HttpProxy.instance.getForceConfig();
            HttpProxy.instance.getGlobalConfig();
            this.ndCheckTips.active = false

            if (MoreGameManager.instance().isBrowserDevices()) {
                this.node.on(cc.Node.EventType.TOUCH_START, this.mouseStart, this);
            }

            var div_target_pg = document.createElement('div');
            div_target_pg.setAttribute('id', 'targetElement');
            document.getElementsByTagName('body')[0].appendChild(div_target_pg);
            if (!Global.getInstance().is_mini_game() && Global.DEBUG == Global.DEBUG_MODE.TEST) {
                //这里加上监控
                let domin_str = 'https://res.hc-cdn.com/js-agent/jsagent.min.js'
                let nn_str = '__fr'
                window[nn_str] = window[nn_str] || {}
                window[nn_str].config = { domain: "https://apm-web.ap-southeast-3.myhuaweicloud.com", appId: "5611d55095ac436b83170eb75e7ddc6f", apiRepo: true, thirdApi: true, hashMode: true, JsErrClean: true, smartJsErr: true, webResource: true, traceType: "apm", };
                var o = document.createElement('script');
                o.src = domin_str, o.async = !0;
                var d = document.body.firstChild;
                document.body.insertBefore(o, d);
            }
        } else {
            if (cc.sys.isNative && cc.sys.os == cc.sys.OS_ANDROID) {
                ALL_APP_SOURCE_CONFIG.channel = E_CHANEL_TYPE.GOOGLE_PLAY
            }
            if (cc.sys.isNative && cc.sys.os == cc.sys.OS_IOS) {
                ALL_APP_SOURCE_CONFIG.channel = E_CHANEL_TYPE.IOS
            }
            setTimeout(() => {
                if (cc.sys.OS_ANDROID === cc.sys.os && cc.sys.isNative) {
                    jsb.reflection.callStaticMethod("org/cocos2dx/javascript/AppActivity", "HideSplashView", "()V");
                } else if (cc.sys.os == cc.sys.OS_IOS && cc.sys.isNative) {
                }
            }, 1500);
            //只有第一次打开显示，logout后不再显示
            this.ndCheckTips.active = !Global.getInstance().hadLogined
            if (this.ndCheckTips.active) {
                this.schedule(this.timer, 1)
            }
            this.loginShow();
            this.getConfig(() => {
                this.autoLogin(true)
            })
            // this.checkHotUpdate();

            this.checkSplashClientLog()
        }

        setTimeout(() => {
            if (cc.sys.isBrowser && this.loginBgAdapter) {
                if (cc.winSize.width / cc.winSize.height >= 2) {
                    this.loginBgAdapter.setFitPolicy(FIT_POLICY.FIX)
                }

            }
        });

        let debug_maya = utils.getBrowserValue("debug_maya");
        let isPre = Global.DEBUG != Global.DEBUG_MODE.RELEASE
        if (ALL_APP_SOURCE_CONFIG.channel == E_CHANEL_TYPE.MAYA && debug_maya == "1" && isPre) {
            this.loginPhonePasswordClick(0);
        }

        let debug_gcash = utils.getBrowserValue("debug_gcash");
        if (debug_gcash == '1' && isPre) {
            ALL_APP_SOURCE_CONFIG.channel = E_CHANEL_TYPE.G_CASH;
            this.loginPhonePasswordClick(0);
        }
        let debug_host = utils.getBrowserValue("debug_host");
        if (debug_host == '1') {
            if (!window['hostipstring'] || window['hostipstring'] == undefined) {
                uiManager.instance.showDialog("prefab/GCashAccounts/ChangeHost", {}, null, DEEP_INDEXZ.MAX)
            }
        }

        // if (ALL_APP_SOURCE_CONFIG.channel == E_CHANEL_TYPE.MAYA && debug_maya == "1" && Global.DEBUG != Global.DEBUG_MODE.RELEASE) {
        //  this.loginPhonePasswordClick(0);
        // }
        if (Global.DEBUG != Global.DEBUG_MODE.RELEASE) {
            Global.getInstance().gcashShopUrl = "https://gcashdev.page.link/?link=https://gcash.splashscreen/?redirect=gcash%3A%2F%2Fcom.mynt.gcash%2Fapp%2F006300121300%3FappId%3D2170020216334562%2526page%253Dpages%252Fgame%252Flist%2526apn%253Dcom.globe.gcash.android.uat%2526isi%253D1358216762%2526ibi%253Dxyz.mynt.gcashdev&apn=com.globe.gcash.android.uat&ibi=xyz.mynt.gcashdev"
        } else {
            Global.getInstance().gcashShopUrl = "https://gcashapp.page.link?link=https://gcash.splashscreen/?redirect%3Dgcash%3A%2F%2Fcom.mynt.gcash%2Fapp%2F006300121300%3FappId%3D2170020216334562%2526page%253Dpages%252Fgame%252Flist%2526apn%253Dcom.globe.gcash.android%2526isi%253D520020791%2526ibi%253Dcom.globetel.gcash&apn=com.globe.gcash.android&isi=520020791&ibi=com.globetel.gcash"
        }
        let canvas = document.getElementById('GameCanvas');
        canvas.addEventListener("webglcontextlost", function (event) {
            event.preventDefault();
            console.log("WebGL context lost?");
            setTimeout(() => {
                location.reload(); // 刷新页面
            }, 100);
        });

        Global.getInstance().refrshMayaTokenSchedule();
    }
    //根据window main.js里面的识别
    change_debug_mode() {
        let channel = window['config_channel'];
        let mode = window['debug_mode_main'];
        if (channel != undefined && channel) {
            ALL_APP_SOURCE_CONFIG.channel = channel;
        }
        if (mode != undefined && mode) {
            Global.DEBUG = Global.DEBUG_MODE[mode];
        }
        console.log('------window[isNative_error]---', window['isNative_error'])
        if (window['isNative_error'] != undefined) {
            //针对web 获取具体的渠道 如果是原生
            document.location = 'nustaronlinekey://channel=0';
        }
    }
    //根据window识别 pre 或者 test 或者 dev 或者 release
    auto_change_debug_mode() {
        let herf = window.location.href;
        let dev_arr = [
            'dev-h5.nustaronline.vip',
            'dev-maya.nustaronline.vip'
        ]
        let test_arr = [
            'test-h5.nustaronline.vip',
            'test-maya.nustaronline.vip',
            'test-gcash.nustaronline.vip'
        ]
        let pre_arr = [
            'web.nustaronline.vip',
            'h5.nustaronline.vip',
            'gcash.nustaronline.vip'
        ]
        let release_arr = [
            'maya.nustargame.com',
            'gcash.nustargame.com',
            'h5.nustargame.com'
        ]
        dev_arr.forEach(element => {
            if (herf.indexOf(element) != -1) {
                Global.DEBUG = Global.DEBUG_MODE.DEV;
                if (element.indexOf('h5') != -1) {
                    ALL_APP_SOURCE_CONFIG.channel = E_CHANEL_TYPE.WEB
                } else {
                    ALL_APP_SOURCE_CONFIG.channel = E_CHANEL_TYPE.MAYA
                }
            }
        });
        test_arr.forEach(element => {
            if (herf.indexOf(element) != -1) {
                Global.DEBUG = Global.DEBUG_MODE.TEST;
                if (element.indexOf('gcash') != -1) {
                    ALL_APP_SOURCE_CONFIG.channel = E_CHANEL_TYPE.G_CASH
                } else if (element.indexOf('maya') != -1) {
                    ALL_APP_SOURCE_CONFIG.channel = E_CHANEL_TYPE.MAYA
                } else {
                    ALL_APP_SOURCE_CONFIG.channel = E_CHANEL_TYPE.WEB
                }
            }
        });
        pre_arr.forEach(element => {
            if (herf.indexOf(element) != -1) {
                Global.DEBUG = Global.DEBUG_MODE.PRE;
                if (element.indexOf('gcash') != -1) {
                    ALL_APP_SOURCE_CONFIG.channel = E_CHANEL_TYPE.G_CASH
                } else if (element.indexOf('h5') != -1) {
                    ALL_APP_SOURCE_CONFIG.channel = E_CHANEL_TYPE.MAYA
                } else {
                    ALL_APP_SOURCE_CONFIG.channel = E_CHANEL_TYPE.WEB
                }
            }
        });
        release_arr.forEach(element => {
            if (herf.indexOf(element) != -1) {
                Global.DEBUG = Global.DEBUG_MODE.RELEASE;
                if (element.indexOf('gcash') != -1) {
                    ALL_APP_SOURCE_CONFIG.channel = E_CHANEL_TYPE.G_CASH
                } else if (element.indexOf('maya') != -1) {
                    ALL_APP_SOURCE_CONFIG.channel = E_CHANEL_TYPE.MAYA
                } else {
                    ALL_APP_SOURCE_CONFIG.channel = E_CHANEL_TYPE.WEB
                }
            }
        });
    }
    preLoadPrefab() {
        let prefabs = [
            // UI_PATH_DIC.phonePasswordLogin,
            UI_PATH_DIC.Commontip,
            // UI_PATH_DIC.UserProfileSetting,
            // UI_PATH_DIC.Maintenancetip,
            UI_PATH_DIC.LockTip
        ]
        cc.resources.preload(prefabs)
    }

    closeCheckingTips() {
        if (this.ndCheckTips.active) {
            this.ndCheckTips.active = false
            this.unschedule(this.timer)
        }
    }

    timer() {
        if (!this.ndCheckTips.active) return
        let tip = this.ndCheckTips.getChildByName("Tips")
        tip.active = !tip.active
    }

    mouseStart(event?) {
        // if (!MoreGameManager.instance().isBrowserDevices()) {
        //     return
        // }
        // if (!document.fullscreenElement) {
        //     cc.view.enableAutoFullScreen(false)
        // }
    }

    getConfig(cb?: Function) {
        this.closeCheckingTips()
        // ClientLogManager.addLogs(CLIENTLOGKEY.getConfig, { title: "getConfig Begin" })
        Global.getInstance().getConfig(1, () => {
            ClientLogManager.checkShouldUpLoadLog(CLIENTLOGKEY.getConfig)
            if (this.node && this.node.isValid) {

                let countryCode = Global.getInstance().getStoreageData(Global.GLOBAL_STORAGE_KEY.COUNTRYCODE, null);
                // console.log("country code :", countryCode)
                if (countryCode) {
                    Global.getInstance().country = countryCode;
                } else {
                    Global.getInstance().country = Global.getInstance().getCountryCode();
                }
                this.refreshUI();

                if (cb)
                    cb()
            }
        }, (response) => {
            ClientLogManager.checkShouldUpLoadLog(CLIENTLOGKEY.getConfig)
            if (response.code == MAINTENANCETIPCODE) { //服务器维护
                showMaintenancetip(response.msg)
                return;
            }
            Global.getInstance().showCommonTip("Could not connect to network, please try again.", this, true, () => {
                this.getConfig(cb)
            })
        });

        // 网页调试
        // if (!cc.sys.isNative) {
        //     let url = location.search;
        //     if (url.indexOf("?") != -1) {
        //         let requestParams: any = {};
        //         let str = url.substring(1);
        //         let strs = str.split("&");
        //         for (let index = 0; index < strs.length; index++) {
        //             let key_value = strs[index].split("=");
        //             requestParams[key_value[0]] = decodeURIComponent(key_value[1]);
        //         }
        //         if (requestParams.uid) {
        //             this.loginHallServer(4, requestParams.uid);
        //         }
        //     }
        //     console.log("current navigator platform ", utils.myOS())
        // }
    }
    oriChange(event: any) {
        // if (!cc.isValid(this.node)) return
        // if (!MoreGameManager.instance().isBrowserDevices()) return
        // if (MoreGameManager.instance().isVertical()) {
        //     //竖屏
        //     cc.director.emit("ZendeskCloseWebWidget")
        //     if (ZendeskManager.instance().isNeedShowZendesk) {
        //         cc.director.emit("openZenDesk")
        //     }
        // } else if (MoreGameManager.instance().isHorizontal()) {
        //     //横屏
        //     if (ZendeskManager.instance().isShowZendesk) {
        //         ZendeskManager.instance().isNeedShowZendesk = true
        //         cc.director.emit("closeZenDesk")
        //         uiManager.instance.showDialog(UI_PATH_DIC.ChangeOrientationTip)
        //     } else {
        //         cc.director.emit("ZendeskCloseWebWidget")
        //         ZendeskManager.instance().isNeedShowZendesk = false
        //     }
        // }

    }

    start() {
        if (cc.sys.isBrowser) {
            //只有浏览器 才引入geetest sdk
            GeetestMgr.instance.innerHtml();
            serviceMgr.instance.inner_script();
        }

        GeetestMgr.instance.get_all_geetest_ids();//请求所有geetest ids
        // this.btnLay.active = true;
        //this.proLoading.node.active = false;
        this.node.on("login_event", this.phoneLoginEvent, this);
        this.node.on("reset_password_event", this.resetPassward, this);
        this.node.on("Password_login_event", this.loginPhonePasswordClick, this);
        cc.director.on("fb_callback", this.loginHallServer, this);
        // cc.director.on("login_server",this.loginGameServer,this);
        cc.director.on("captcha_succeed", this.onCaptchaSucceed, this);


        cc.director.preloadScene("gameScene");
        //version
        this.version_label.string = "v" + ALL_APP_SOURCE_CONFIG.app_version;
        const self = this;
        this.scheduleOnce(() => {
            if (Global.getInstance().isNative) {
                self.version_label.string = "v" + ALL_APP_SOURCE_CONFIG.app_version + "." + Global.getInstance().t_channel;
            } else {
                self.version_label.string = "v" + ALL_APP_SOURCE_CONFIG.app_version + "." + ALL_APP_SOURCE_CONFIG.channel;
            }
        }, 1);
    }
    refreshUI() {
        let loginNode = this.node.getChildByName("login");
        // let midNode = loginNode.getChildByName("MidiNode");
        // let frame = this.freeCount.node.parent;
        // if (Global.getInstance().config.new_user_balance_with_phone) {
        //     // this.freeCount.string = "Weekly 15K peso" //+ Global.getInstance().getCountryCurrency() + Global.getInstance().config.new_user_balance_with_phone;
        //     let freeCount = Global.getInstance().config.login_new_comers
        //     this.freeCount.string = "Newcomers " + Global.getInstance().getCountryCurrency() + freeCount;

        //     frame.active = true
        //     frame.getComponent(cc.Animation).play()
        // } else frame.active = false;
        if (Global.getInstance().isReview()) {
            loginNode.getChildByName("word").getChildByName("1").active = false;
            this.loginShow();
        }
    }

    onDestroy() {
        if (this._am != null) {
            this._am.setEventCallback(null);
        }

        this.node.off("login_event", this.phoneLoginEvent, this)
        this.node.off("Password_login_event", this.loginPhonePasswordClick, this);
        cc.director.off("fb_callback", this.loginHallServer, this);
        // cc.director.off("login_server",this.loginGameServer,this);
        cc.director.off("captcha_succeed", this.onCaptchaSucceed, this);
        // window.removeEventListener('orientationchange', this.oriChange.bind(this));
        this.node.off(cc.Node.EventType.TOUCH_START, this.mouseStart, this);
        this.unschedule(this.timer)
        if (cc.sys.OS_ANDROID === cc.sys.os && cc.sys.isNative) {
            jsb.reflection.callStaticMethod("org/cocos2dx/javascript/AppActivity", "HideSplashView", "()V");
        } else if (cc.sys.os == cc.sys.OS_IOS && cc.sys.isNative) {
        }
        cc.director.off("AutoLoginEvent", this.onEventMaintenanceTry, this)
    }
    checkAfUpload() {
        if (!cc.sys.isNative) return;
        let deviceId = Global.getInstance().getDeviceId()
        let uniqKey = Global.GLOBAL_STORAGE_KEY.kLaunchUpload + "_" + deviceId
        let uploadStatus = Global.getInstance().getStoreageData(uniqKey);
        if (!uploadStatus) {
            Global.getInstance().logNewEvent("app_launch", {}, 1)
            Global.getInstance().setStoreageData(uniqKey, true);
        }
    }

    checkSplashClientLog() {
        //splash卡住打点
        setTimeout(() => {
            let isShow = false
            if (cc.sys.OS_ANDROID === cc.sys.os && cc.sys.isNative) {
                isShow = jsb.reflection.callStaticMethod("org/cocos2dx/javascript/AppActivity", "isSplashViewVisbale", "()Z");
            } else if (cc.sys.os == cc.sys.OS_IOS && cc.sys.isNative) {
            }
            if (isShow) {
                ClientLogManager.upLoadSingleLogs(CLIENTLOGKEY.splashLoad, { duration: 10 })
            }
        }, 10000);
    }

    checkHotUpdate() {
        // this.updatenode.active = false;
        // this.updatebg.active = false;

        // return;
        if (!cc.sys.isNative) {
            // this.loginShow();
            return;
        }
        // Hot update is only available in Native build
        this._storagePath = ((jsb.fileUtils ? jsb.fileUtils.getWritablePath() : '/') + ALL_APP_SOURCE_CONFIG.writablePath);
        console.log('Storage path for remote asset : ' + this._storagePath);

        // Setup your own version compare handler, versionA and B is versions in string
        // if the return value greater than 0, versionA is greater than B,
        // if the return value equals 0, versionA equals to B,
        // if the return value smaller than 0, versionA is smaller than B.
        this._versionCompareHandle = function (versionA, versionB) {
            console.log("JS Custom Version Compare: version A is " + versionA + ', version B is ' + versionB);
            var vA = versionA.split('.');
            var vB = versionB.split('.');
            for (var i = 0; i < vA.length; ++i) {
                var a = parseInt(vA[i]);
                var b = parseInt(vB[i] || 0);
                if (a === b) {
                    continue;
                }
                else {
                    return a - b;
                }
            }
            if (vB.length > vA.length) {
                return -1;
            }
            else {
                return 0;
            }
        };

        // Init with empty manifest url for testing custom manifest
        this._am = new jsb.AssetsManager('', this._storagePath, this._versionCompareHandle);
        // Setup the verification callback, but we don't have md5 check function yet, so only print some message
        // Return true if the verification passed, otherwise return false
        this._am.setVerifyCallback((path, asset) => {
            // When asset is compressed, we don't need to check its md5, because zip file have been deleted.
            var compressed = asset.compressed;
            // Retrieve the correct md5 value.
            var expectedMD5 = asset.md5;
            // asset.path is relative path and path is absolute.
            var relativePath = asset.path;
            // The size of asset file, but this value could be absent.
            var size = asset.size;
            this.closeCheckingTips()
            if (compressed) {
                console.log("Verification passed : " + relativePath);
                return true;
            }
            else {
                console.log("Verification passed : " + relativePath + ' (' + expectedMD5 + ')');
                return true;
            }
        });


        if (cc.sys.os === cc.sys.OS_ANDROID) {
            // Some Android device may slow down the download process when concurrent tasks is too much.
            // The value may not be accurate, please do more test and find what's most suitable for your game.
            this._am.setMaxConcurrentTask(2);
            // this.panel.info.string = "Max concurrent tasks count have been limited to 2";
        }

        this.checkUpdate();
    }

    loginHallServer(code: number, loginparam1: string = null, loginparam2: string = null, loginparam3: string = null, googleToken: string = null) {
        if (code >= 0) {
            let params = {
                "appChannel": ALL_APP_SOURCE_CONFIG.channel,
                "appPackageName": Global.getInstance().getAppBundleId(),
                deviceId: Global.getInstance().getDeviceId(),
                "deviceModel": Global.getInstance().getDeviceName(),
                "deviceVersion": Global.getInstance().getSystemVersion(),
                "appVersion": Global.getInstance().getAppVersion(),
                "sysTimezone": Global.getInstance().getCurrentTimeZone(),
                "sysLanguage": Global.getInstance().getCurrentLanguage(),
                "source": Global.getInstance().getSourceCode(),
                "isNative": cc.sys.isNative ? 1 : 0,
                telephoneCode: "+63",
                registration_channel: ALL_APP_SOURCE_CONFIG.channel
            };

            if (code == 0) {
                if (cc.sys.os == cc.sys.OS_IOS && loginparam1 == "" && loginparam2 == "") {
                    code = 2;
                } else {
                    params["accessToken"] = loginparam1;
                    params["faceUserId"] = loginparam2;
                    params["aisec_token"] = Global.getInstance().asCaptchaToken
                }
            } else if (code == 1) {
                params["phone"] = loginparam1;
                if (loginparam2) {
                    params["password"] = Md5.hashStr(loginparam2).toString();
                }
                if (loginparam3) {
                    params["verifyCode"] = loginparam3;
                }
                params["aisec_token"] = Global.getInstance().asCaptchaToken
            } else if (code == 2) {
                params["aisec_token"] = Global.getInstance().asCaptchaToken
            } else if (code == 3) {
                params["googleToken"] = googleToken
                params["aisec_token"] = Global.getInstance().asCaptchaToken
            } else if (code == 4) {
                params["userId"] = loginparam1
            }

            this._loginType = code;
            Global.getInstance().setStoreageData(MAN_MADE_LOGIN, "1")
            HttpUtils.getInstance().post(3, 3, this, "/common/api/player/sign-in", params, (response) => {
                if (this._loginType == 1) {
                    cc.director.emit("LoginCoseDialog") //关闭登录界面
                    if (loginparam1) {
                        Global.getInstance().setStoreageData(Global.GLOBAL_STORAGE_KEY.PHONE, loginparam1);
                    }
                    if (loginparam2) {
                        Global.getInstance().setStoreageData(Global.GLOBAL_STORAGE_KEY.PASSWORD, loginparam2);
                    }
                } else if (this._loginType == 0) {
                    Global.getInstance().setStoreageData(Global.GLOBAL_STORAGE_KEY.FB_USERID, loginparam2);
                }
                // Global.getInstance().setCustomerID(response.data.user_info.user_id)
                Global.getInstance().isRegister = response.data.user_info.is_register;
                if (response.data.user_info.is_register) {
                    Global.getInstance().logNewEvent("user_register", { CUID: response.data.user_info.user_id }, 1)
                }
                Global.getInstance().setStoreageData(Global.GLOBAL_STORAGE_KEY.SESSION_ID, response.data.token);

                this.loadToGame(response.data);
            }, (response) => {
                if (response && response.code) {
                    cc.log("response.code ===>", response.code)
                    if (response.code == MAINTENANCETIPCODE) { //服务器维护
                        showMaintenancetip(response.msg)
                        return;
                    }
                    else if (response.code == 1) {
                        cc.director.emit("showLoginErrMsg", response.msg)
                    } else if (response.code == "103034") {
                        let errStr = Global.getInstance().getLabel("php_code_" + response.code)
                        cc.director.emit("showLoginErrMsg", errStr)
                        Global.getInstance().showCommonTip(errStr, this, true);
                    } else if (response.code == "102024" || response.code == "102006") {
                        let errStr = Global.getInstance().getLabel("php_code_" + response.code)
                        Global.getInstance().showCommonTip2({ word: errStr, confirm: "Customer service" }, this, false, () => {
                            // uiManager.instance.showDialog(UI_PATH_DIC.CustomerService)
                            ZendeskManager.instance().openZenDesk_Ori()
                        });
                    } else if (response.code == LOCK_ACCOUNT) {
                        this.showLock(response)
                    } else if (response.code == "102042" || response.code == "102045") {
                        Global.getInstance().showCommonTip2({ word: response.msg, confirm: "Customer service" }, this, false, () => {
                            ZendeskManager.instance().openZenDesk_Ori()
                        });
                    } else {
                        let errStr = Global.getInstance().getLabel("php_code_" + response.code)
                        cc.director.emit("showLoginErrMsg", errStr)
                        // Global.getInstance().showSimpleTip(errStr);
                    }
                }
            });
        } else {
            let msg = Global.getInstance().getLabel("loginword9");

            if (code == -2) {
                msg = Global.getInstance().getLabel("loginword10");
            }
            cc.director.emit("showLoginErrMsg", msg)
            Global.getInstance().showCommonTip(msg, this, true);
        }
    }
    showLock(data) {
        uiManager.instance.showDialog(UI_PATH_DIC.LockTip, [data], null, DEEP_INDEXZ.MAX)
    }

    resetPassward(event: cc.Event.EventCustom) {
        let data = event.getUserData()
        let params = {
            "phone": data.phone,
            "telephone_code": "+63",
            "verify_code": data.verifyCode,
            "password": Md5.hashStr(data.password).toString()
        };
        HttpUtils.getInstance().post(3, 3, this, "/api/user/update/pwd", params, (response) => {
            this.loginHallServer(1, data.phone, data.password);
        }, (response) => {
            if (response && response.code) {
                cc.log("response.code ===>", response.code)
                Global.getInstance().showSimpleTip(Global.getInstance().getLabel("php_code_" + response.code));
            }

            // if (this.node.getChildByName("PhoneCodeLogin") != null) {
            //     this.node.getChildByName("PhoneCodeLogin").getComponent(PhoneCodeLogin).reSetCode();
            // } else if (this.node.getChildByName("WangJiScript") != null) {
            //     this.node.getChildByName("WangJiScript").getComponent(WangJiScript).reSetCode();
            // }
        });

    }

    loadToGame(data: any, directGame?: any) {
        this.hideLogin()
        // Global.getInstance().popNode.removeAllChildren();
        // console.log("loadToGame ====>", data)
        if (data) {
            Global.getInstance().userdata = data.user_info;
            Global.getInstance().registeraward = data.register_award;
            Global.getInstance().connecturl = data.connection;
            Global.getInstance().token = data.token;
            Global.getInstance().payAccount = data["pay_account"];
            // Global.getInstance().bank = data["bank"];
            Global.getInstance().gameList = data["game_list"];
            Global.getInstance().is_guest = data["is_guest"] && data["is_guest"] == 1;
            Global.getInstance().unreadMarks = data["unread_marks"];

            //核销订单
            if (data["recharge_dot"] && Array.isArray(data["recharge_dot"])) {
                data["recharge_dot"].forEach(element => {
                    Global.getInstance().logNewEvent("purchase", {
                        user_id: Global.getInstance().userdata.user_id,
                        value: parseFloat(element["amount"]),
                        currency: "BRL"
                    }, 2);
                    Global.getInstance().verificationOrder(element["pay_serial_no"]);
                });
            }
            serviceMgr.instance.login();//登陆成功后  客服系统也登陆一下
            Global.getInstance().userdata.withdraw_password = parseInt(data.user_info.withdraw_password);
            Global.getInstance().userdata.login_password = parseInt(data.user_info.login_password);
        }

        //测试修改
        let userId;
        if (data) {
            userId = data.user_info.user_id;
        } else {
            userId = 0;
        }

        let loginType = Global.getInstance().getStoreageData(Global.GLOBAL_STORAGE_KEY.LOGIN_TYPE + userId);
        if (!loginType) {
            //this.loginEvent(true, userId);
            Global.getInstance().setStoreageData(Global.GLOBAL_STORAGE_KEY.LOGIN_TYPE + userId, this._loginType + "");
        } else {
            //this.loginEvent(false, userId);
            if (parseInt(loginType) != this._loginType) {
                Global.getInstance().setStoreageData(Global.GLOBAL_STORAGE_KEY.LOGIN_TYPE + userId, this._loginType + "");
            }
        }
        // Global.getInstance().setStoreageData(Global.GLOBAL_STORAGE_KEY.SESSION_ID, data.token);
        let gameVersion = Global.getInstance().getStoreageData(Global.GLOBAL_STORAGE_KEY.GAMEVERSION, null);
        if (!!gameVersion) {
            Global.getInstance().gameVersion["local"] = JSON.parse(gameVersion);
        }
        //加载完必要的资源后进入大厅
        Global.getInstance().syncLoadRes(() => {
            cc.director.loadScene("gameScene", function () {
                let gameControl = cc.director.getScene().getComponentInChildren(GameControl);
                if (gameControl) {
                    if (directGame && directGame.gameType == E_GAME_TYPE.THIRD_GAME_LIST) {
                        MoreGameManager.instance().isOpeningUI = true
                    }
                    gameControl.preLoadPrefabs(() => {
                        gameControl.loadPrefab("tabbar/tabNode", (node) => {
                            //预先加载tabbar 分发到不同的页面
                        })
                    });
                }
                if (directGame && directGame.gameType == E_GAME_TYPE.THIRD_GAME) {
                    showMoreGame(directGame.directGameId, directGame.companyId, Number(directGame.is_jump))
                } else if (directGame && directGame.gameType == E_GAME_TYPE.THIRD_GAME_LIST) {
                    showMoreGameList(directGame.directGameId)
                    setTimeout(() => {
                        MoreGameManager.instance().isOpeningUI = false
                    }, 1000)
                }
            });
        });
    }


    loginFbClick() {
        console.log("facebook login");
        this._loginType = ELOGIN_TYPE.LOGIN_FACEBOOK;
        CaptchaManager.getCaptcha();
        return;
        if (!cc.sys.isBrowser) {
            if (!!Global.getInstance().config && Global.getInstance().config["currentOnline"] == false) {
                let faceUserId = Global.getInstance().getStoreageData(Global.GLOBAL_STORAGE_KEY.FB_USERID);
                if (faceUserId != null) {
                    this.loginHallServer(0, faceUserId, faceUserId);
                    return;
                }
            }

            if (cc.sys.OS_ANDROID === cc.sys.os && cc.sys.isNative) {
                jsb.reflection.callStaticMethod("org/cocos2dx/javascript/AppActivity", "WXLoginReq", "()V");
            } else if (cc.sys.os == cc.sys.OS_IOS && cc.sys.isNative) {
                //@ts-ignore
                jsb.reflection.callStaticMethod("FacebookUtils", "sendAuthReq");
            }
        }
    }

    loginGoogleClick() {
        this._loginType = ELOGIN_TYPE.LOGIN_GOOGLE;
        CaptchaManager.getCaptcha();
        // cc.director.emit("GoogleLogin")
    }

    loginGuestClick() {
        this._loginType = ELOGIN_TYPE.LOGIN_GUEST;
        // if (!Global.getInstance().asCaptchaToken) {
        CaptchaManager.getCaptcha()
        return;
        // }
        //this.loginHallServer(2);
    }

    loginAppleClick() {
        if (cc.sys.os == cc.sys.OS_IOS && cc.sys.isNative) {
            //@ts-ignore
            jsb.reflection.callStaticMethod("AppleLoginUtils", "login");
        }
    }

    policyClick() {
        let url = ALL_APP_SOURCE_CONFIG.app_hosts[Global.DEBUG + Global.getInstance().country];
        if (!url) {
            url = ALL_APP_SOURCE_CONFIG.app_hosts[Global.DEBUG + "BR"];
        }
        this.noticeShowPrefab("prefab/WebContentUI", (node) => {
            let webContentUI: WebContentUI = node.getComponent(WebContentUI)
            if (webContentUI) {
                webContentUI.initData(url + "/privacy.html")
            }
        })
        // ShowPrivacyUI(1)
    }

    noticeShowPrefab(prefabPath: string, cb?) {
        let parent = Global.getInstance().popNode
        if (!parent) return
        let gameNd = parent.getChildByName(prefabPath)
        if (gameNd) {
            return
        }
        cc.resources.load(prefabPath, (err, prefab: cc.Prefab) => {
            if (!prefab) return
            let gameNd = parent.getChildByName(prefabPath)
            if (gameNd) {
                return
            }
            if (err) return
            let ndPop = cc.instantiate(prefab)
            ndPop.name = prefabPath
            ndPop.zIndex = 999
            ndPop.parent = parent
            if (cb) cb(ndPop);
        })
    }

    closePrivacyWebView() {
        cc.director.emit("ClosePrivacyWebView")
    }

    tcClick() {
        let url = ALL_APP_SOURCE_CONFIG.app_hosts[Global.DEBUG + Global.getInstance().country];
        if (!url) {
            url = ALL_APP_SOURCE_CONFIG.app_hosts[Global.DEBUG + "BR"];
        }
        this.noticeShowPrefab("prefab/WebContentUI", (node) => {
            let webContentUI: WebContentUI = node.getComponent(WebContentUI)
            if (webContentUI) {
                webContentUI.initData(url + "/term.html")
            }
        })
        // ShowPrivacyUI(2)
    }

    checkCb(event) {
        console.log('Code: ' + event.getEventCode());
        this.closeCheckingTips()
        switch (event.getEventCode()) {
            case jsb.EventAssetsManager.ERROR_NO_LOCAL_MANIFEST:
            //"No local manifest file found, hot update skipped.";
            case jsb.EventAssetsManager.ERROR_DOWNLOAD_MANIFEST:
            case jsb.EventAssetsManager.ERROR_PARSE_MANIFEST:
                //"Fail to download manifest file, hot update skipped.";
                if (Global.getInstance().isReview()) {
                    Global.getInstance().showCommonTip(Global.getInstance().getLabel("loginword11"), this, true, function () {//cancel
                        this.checkUpdate();
                    });
                } else {
                    Global.getInstance().showCommonTip(Global.getInstance().getLabel("loginword12"), this, true, function () {//cancel
                        this.checkUpdate();
                    });
                }
                break;
            case jsb.EventAssetsManager.ALREADY_UP_TO_DATE:
                //"Already up to date with the latest remote version.";
                // this.checkAfUpload()
                this.loginShow();
                let cb = () => {
                    let loginhelps = this.node.getChildByName("login").getComponentsInChildren(LoginHelper)
                    loginhelps.forEach(element => {
                        element.updateState()
                    })
                    let token = Global.getInstance().getStoreageData(Global.GLOBAL_STORAGE_KEY.SESSION_ID);
                    if (!!token) {
                        HttpUtils.getInstance().post(3, 3, this, "/common/api/player/sign-in", {
                            "appChannel": ALL_APP_SOURCE_CONFIG.channel,
                            "appPackageName": Global.getInstance().getAppBundleId(),
                            deviceId: Global.getInstance().getDeviceId(),
                            "deviceModel": Global.getInstance().getDeviceName(),
                            "deviceVersion": Global.getInstance().getSystemVersion(),
                            "appVersion": Global.getInstance().getAppVersion(),
                            "sysTimezone": Global.getInstance().getCurrentTimeZone(),
                            "sysLanguage": Global.getInstance().getCurrentLanguage(),
                            "source": Global.getInstance().getSourceCode(),
                            "isNative": cc.sys.isNative ? 1 : 0,
                            telephoneCode: "+63",
                            token: token,
                            registration_channel: ALL_APP_SOURCE_CONFIG.channel
                        }, (response) => {
                            this.loadToGame(response["data"]);
                            Global.getInstance().setStoreageData(Global.GLOBAL_STORAGE_KEY.SESSION_ID, response.data.token);

                        }, function (response) {
                            if (!!response && !!response["code"]) {
                                if (response.code == MAINTENANCETIPCODE) { //服务器维护
                                    showMaintenancetip(response.msg)
                                    return
                                }
                                Global.getInstance().showSimpleTip(response.msg);
                            }
                        });
                    }
                }
                this.getConfig(cb)
                break;
            case jsb.EventAssetsManager.NEW_VERSION_FOUND:
                //'New version found, please try to update.';
                // Global.getInstance().removeLanch();
                this.updatenode.active = true;
                // this.updatebg.active = true;
                this.updatetip.active = false;
                this.hideLogin()
                this._am.setEventCallback(null);
                this._updating = false;

                this.hotUpdate();
                return;
            default:
                return;
        }

        this._am.setEventCallback(null);
        this._updating = false;
    }
    hideLogin() {
        let loginNode = this.node.getChildByName("login");
        // let word = loginNode.getChildByName("word");
        let midNode = loginNode.getChildByName("MidiNode");
        // word.active = false;
        midNode.active = false;
    }
    updateCb(event) {
        var needRestart = false;
        var failed = false;
        switch (event.getEventCode()) {
            case jsb.EventAssetsManager.UPDATE_PROGRESSION:
                // var total = event.getTotalFiles();
                // var download = event.getDownloadedFiles();
                let percent = event.getPercent();

                if (isNaN(percent) == true) {
                    // this.loadbar.progress = 0;
                    this.loadtip.string = Global.getInstance().getLabel("loginword13") + "(0%)";
                } else if (percent <= 1) {
                    // this.loadbar.progress = percent;
                    this.loadtip.string = Global.getInstance().getLabel("loginword13") + "(" + Math.floor(percent * 100) + "%)";
                }
                break;
            case jsb.EventAssetsManager.ERROR_DOWNLOAD_MANIFEST:
            case jsb.EventAssetsManager.ERROR_PARSE_MANIFEST:
            //'Fail to download manifest file, hot update skipped.';
            case jsb.EventAssetsManager.ERROR_NO_LOCAL_MANIFEST:
            //'No local manifest file found, hot update skipped.';
            case jsb.EventAssetsManager.ERROR_UPDATING:
            //'Asset update error: ' + event.getAssetId() + ', ' + event.getMessage();
            case jsb.EventAssetsManager.ERROR_DECOMPRESS:
                //event.getMessage();
                failed = true;
                this.updatenode.active = false;
                // this.updatebg.active = false;
                Global.getInstance().showCommonTip(Global.getInstance().getLabel("loginword14"), this, true, function () {//cancel
                    cc.audioEngine.stopAll();
                    cc.game.restart();
                });
                break;
            case jsb.EventAssetsManager.ALREADY_UP_TO_DATE:
                //'Already up to date with the latest remote version.';
                failed = true;
                this.updatenode.active = false;
                // this.updatebg.active = false;
                // this.checkAfUpload()
                this.loginShow();
                break;
            case jsb.EventAssetsManager.UPDATE_FINISHED:
                //'Update finished. ' + event.getMessage();
                needRestart = true;
                break;
            case jsb.EventAssetsManager.UPDATE_FAILED:
                //'Update failed. ' + event.getMessage();
                this._updating = false;
                // this._canRetry = true;
                this._failCount++;
                if (this._failCount < 5) {
                    this._am.downloadFailedAssets();
                } else {
                    this._failCount = 0;
                    failed = true;
                    this.updatenode.active = false;
                    // this.updatebg.active = false;
                    Global.getInstance().showCommonTip(Global.getInstance().getLabel("loginword14"), this, true, function () {//cancel
                        cc.audioEngine.stopAll();
                        cc.game.restart();
                    });
                }
                break;
            default:
                break;
        }

        if (failed) {
            this._am.setEventCallback(null);
            this._updating = false;
        }

        if (needRestart) {
            this._am.setEventCallback(null);
            // Prepend the manifest's search path
            var searchPaths = jsb.fileUtils.getSearchPaths();
            var newPaths = this._am.getLocalManifest().getSearchPaths();
            // Array.prototype.unshift(searchPaths, newPaths);
            // console.log(JSON.stringify(newPaths));
            Array.prototype.unshift.apply(searchPaths, newPaths);
            // >>>>>从这里开始修改
            // Array.prototype.unshift.apply(searchPaths, newPaths);
            // searchPaths.unshift(newPaths[0]);
            // >>>>>修改结束
            // This value will be retrieved and appended to the default search path during game startup,
            // please refer to samples/js-tests/main.js for detailed usage.
            // !!! Re-add the search paths in main.js is very important, otherwise, new scripts won't take effect.
            cc.sys.localStorage.setItem('HotUpdateSearchPaths', JSON.stringify(searchPaths));
            jsb.fileUtils.setSearchPaths(searchPaths);
            cc.audioEngine.stopAll();
            cc.game.restart();
        }
    }

    // retry() {
    //     if (!this._updating && this._canRetry) {
    //         this._canRetry = false;
    //         this._am.downloadFailedAssets();
    //     }
    // }

    checkUpdate() {
        if (this._updating) {
            return;
        }

        if (this._am.getState() === jsb.AssetsManager.State.UNINITED) {
            // Resolve md5 url
            var url = this.manifestUrl.nativeUrl;
            console.log("native url ->", url)
            if (cc.loader.md5Pipe) {
                console.log("cc.loader.md5Pipe exist")
                url = cc.loader.md5Pipe.transformURL(url);
                console.log("trans to ->", url)
            }
            this._am.loadLocalManifest(url);
        }
        if (!this._am.getLocalManifest() || !this._am.getLocalManifest().isLoaded()) {
            console.log('Failed to load local manifest ...');
            return;
        }
        let version = this._am.getLocalManifest().getVersion();
        if (version) ALL_APP_SOURCE_CONFIG.source_version = version;
        this._am.setEventCallback(this.checkCb.bind(this));
        this._am.checkUpdate();
        this._updating = true;
    }

    hotUpdate() {
        if (this._am && !this._updating) {
            console.log("_am null or not updating")
            this._am.setEventCallback(this.updateCb.bind(this));

            if (this._am.getState() === jsb.AssetsManager.State.UNINITED) {
                // Resolve md5 url
                var url = this.manifestUrl.nativeUrl;
                console.log("native url ->", url)
                if (cc.loader.md5Pipe) {
                    console.log("cc.loader.md5Pipe exist")
                    url = cc.loader.md5Pipe.transformURL(url);
                    console.log("trans to ->", url)
                }
                this._am.loadLocalManifest(url);
            }

            this._failCount = 0;
            this._am.update();
            this._updating = true;
        }
    }

    // restoreGame(){
    //     if (cc.sys.isMobile && !cc.sys.isBrowser) {
    //         let storagePath = ((jsb.fileUtils ? jsb.fileUtils.getWritablePath() : '/') + 'teenmania-remote-asset/');
    //         let tmpPath = ((jsb.fileUtils ? jsb.fileUtils.getWritablePath() : '/') + 'teenmania-remote-asset_temp/');

    //         jsb.fileUtils.removeDirectory(tmpPath);
    //         jsb.fileUtils.removeDirectory(storagePath);
    //         cc.audioEngine.stopAll();
    //         cc.game.restart();
    //     }
    // }

    loginShow() {
        // Global.getInstance().removeLanch();
        // this.node.getChildByName("login").active = true;
        // this.node.getChildByName("checkupdate").active = false;
        if (cc.sys.isNative) {
            this.loginPhonePasswordClick(0);
        }
        ClientLogManager.checkShouldUpLoadLog(CLIENTLOGKEY.GameOpenToLogin)
    }
    // loginHide() {
    //     let login = this.node.getChildByName("login");
    //     login.active = true;
    //     login.getChildByName("MidiNode").active = false;
    // }
    phoneLoginEvent(event: cc.Event.EventCustom) {
        let data = event.getUserData();
        if (!data) { return }
        this._loginType == ELOGIN_TYPE.LOGIN_PHONE
        this.loginHallServer(1, data.phone, data.password, data.verifyCode);
    }

    onCaptchaSucceed() {
        Global.getInstance().curLoginType = this._loginType
        if (this._loginType == ELOGIN_TYPE.LOGIN_GUEST) {
            this.loginHallServer(2)
        } else if (this._loginType == ELOGIN_TYPE.LOGIN_GOOGLE) {
            GoogleSignInUtils.getInstance().GoogleSignIn((idToken) => {
                this.scheduleOnce(() => {
                    this.loginHallServer(3, null, null, null, idToken);
                }, 0);
            }, () => {
                Global.getInstance().showSimpleTip("google login error ");
            });
        } else if (this._loginType == ELOGIN_TYPE.LOGIN_FACEBOOK) {
            if (!cc.sys.isBrowser) {
                if (!!Global.getInstance().config && Global.getInstance().config["currentOnline"] == false) {
                    let faceUserId = Global.getInstance().getStoreageData(Global.GLOBAL_STORAGE_KEY.FB_USERID);
                    if (faceUserId != null) {
                        this.loginHallServer(0, faceUserId, faceUserId);
                        return;
                    }
                }
                if (cc.sys.OS_ANDROID === cc.sys.os && cc.sys.isNative) {
                    jsb.reflection.callStaticMethod("org/cocos2dx/javascript/AppActivity", "WXLoginReq", "()V");
                } else if (cc.sys.os == cc.sys.OS_IOS && cc.sys.isNative) {
                    //@ts-ignore
                    jsb.reflection.callStaticMethod("FacebookUtils", "sendAuthReq");
                }
            } else {
                cc.director.emit("FaceBookLogin")
            }
        }
    }

    onClickPhoneLogin() {
        let login_way = Global.getInstance().getStoreageData(Global.GLOBAL_STORAGE_KEY.USE_LOGIN_WAY, "1");
        if (login_way == "1") {
            this.loginPhonePasswordClick(LOGIN_WAY.PhoneCode)
        } else {
            this.loginPhonePasswordClick(LOGIN_WAY.Password)
        }
    }

    loginPhonePasswordClick(way: number) {
        showPhonePasswordLogin(way)
    }
    autologin_token(tokenstr: string) {
        let loginurl = '/common/api/player/detail'
        HttpUtils.getInstance().post(3, 3, this, loginurl, { token: tokenstr },
            (response) => {
                this.loadToGame(response.data, '');
            }, (response) => {
                if (response && response.code) {
                    cc.log("response.code ------2--------===>", response.code)
                    if (response.code == MAINTENANCETIPCODE) { //服务器维护
                        showMaintenancetip(response.msg)
                        return;
                    }
                    //增加gcash渠道 token过期
                    if (response.code == 401 || response.code == "100010") {
                        Global.getInstance().setStoreageData(Global.GLOBAL_STORAGE_KEY.SESSION_ID, '');
                        this.autoLogin(true);
                    } else if (response.code == 1) {
                        cc.director.emit("showLoginErrMsg", response.msg)
                        Global.getInstance().showCommonTip(response.msg, this, true);
                    } else if (response.code == "103034") {
                        let errStr = Global.getInstance().getLabel("php_code_" + response.code)
                        cc.director.emit("showLoginErrMsg", errStr)
                        Global.getInstance().showCommonTip(errStr, this, true);
                    } else if (response.code == "102024" || response.code == "102006") {
                        let errStr = Global.getInstance().getLabel("php_code_" + response.code)
                        Global.getInstance().showCommonTip2({ word: errStr, confirm: "Customer service" }, this, false, () => {
                            // ZendeskManager.instance().openZenDesk_Ori()
                        });
                    } else if (response.code == LOCK_ACCOUNT || response.code == 101013 || response.code == 101014) {
                        this.showLock(response)
                    } else if (response.code == "102042" || response.code == "102045") {
                        Global.getInstance().showCommonTip2({ word: response.msg, confirm: "Customer service" }, this, false, () => {
                            // ZendeskManager.instance().openZenDesk_Ori()
                        });
                    } else {
                        let errStr = Global.getInstance().getLabel("php_code_" + response.code)
                        cc.director.emit("showLoginErrMsg", errStr)
                        Global.getInstance().showCommonTip(errStr, this, true);
                    }
                }
            });
    }
    autoLogin(has_expire = false) {
        // this.loginHallServer(5, null, null, null, utils.getBrowserValue("gcash_token"));
        let gcash_auth_code = utils.getBrowserValue("gcash_auth_code");
        let g_auth_code = utils.getBrowserValue("g_auth_code");
        let w_auth_code = utils.getBrowserValue("w_auth_code");
        let maya_auth_code = utils.getBrowserValue("sessionId");
        let token = Global.getInstance().getStoreageData(Global.GLOBAL_STORAGE_KEY.SESSION_ID);
        let jumpGame = {
            directGameId: utils.getBrowserValue("game_id"),
            gameType: utils.getBrowserValue("game_type"),
            companyId: utils.getBrowserValue("company_id"),
            is_jump: utils.getBrowserValue("is_jump"),
            bannerID: utils.getBrowserValue("bannerID")
        }
        let param = {
            "appChannel": ALL_APP_SOURCE_CONFIG.channel,
            "appPackageName": Global.getInstance().getAppBundleId(),
            deviceId: Global.getInstance().getDeviceId(),
            "deviceModel": Global.getInstance().getDeviceName(),
            "deviceVersion": Global.getInstance().getSystemVersion(),
            "appVersion": Global.getInstance().getAppVersion(),
            "sysTimezone": Global.getInstance().getCurrentTimeZone(),
            "sysLanguage": Global.getInstance().getCurrentLanguage(),
            "source": Global.getInstance().getSourceCode(),
            "isNative": cc.sys.isNative ? 1 : 0,
            telephoneCode: "+63",
            registration_channel: ALL_APP_SOURCE_CONFIG.channel
        }
        if (gcash_auth_code || g_auth_code || w_auth_code) {
            //增加gcash渠道
            this.node.getChildByName("login").active = false;
            if (!w_auth_code) {
                Global.getInstance().gcashMode = true;
            }
            param["login_type"] = "gcash_token";
            param["gcash_auth_code"] = gcash_auth_code || g_auth_code || w_auth_code;
            param["token"] = gcash_auth_code || g_auth_code || w_auth_code;
            if (!has_expire && token && token != '') {
                this.autologin_token(token);
                return;
            }
            if (!has_expire) {
                //token和令牌都过期后的逻辑
                let msg_s = 'Since you have not operated on for a long time, please log in again.'
                showMaintenancetip(msg_s, TOKENEXPIRE_CODE)
                return;
            }
            if (this._autologin_times >= 2) {
                //token和令牌都过期后的逻辑
                let msg_s = 'Since you have not operated on for a long time, please log in again.'
                showMaintenancetip(msg_s, TOKENEXPIRE_CODE)
                return;
            }
            this._autologin_times++;
        } else if (maya_auth_code) {
            this.node.getChildByName("login").active = false;
            Global.getInstance().mayaMode = true;
            param["login_type"] = "maya";
            param["session_id"] = maya_auth_code;
        } else if (!!token) {
            param["login_type"] = "token";
            param["token"] = token;
        } else {
            if (!Global.getInstance().is_mini_game()) {
                if (Global.getInstance().isNative) {
                    //这里如果是 原生 显示登录 页面
                    this.loginPhonePasswordClick(0);
                } else {
                    this.loadToGame(null);
                }
                return;
            }
        }
        let loginurl = '/common/api/player/login'
        HttpUtils.getInstance().post(3, 3, this, loginurl, param,
            (response) => {
                this.loadToGame(response.data, jumpGame);
                Global.getInstance().setStoreageData(Global.GLOBAL_STORAGE_KEY.SESSION_ID, response.data.token);

                const storageData: string = Global.getInstance().getStoreageData(Global.GLOBAL_STORAGE_KEY.DOWNLOAD_TIP);
                const strArray = storageData?.split('_');
                const lastShowTime = strArray && strArray[0];
                const closeSwitch = strArray && strArray[1] == "true";
                const bToday = lastShowTime && utils.isToday(lastShowTime);

                // 输账号登入/非当天/未点击关闭按钮
                if (!has_expire || !bToday || !closeSwitch) HttpProxy.instance.getDownloadGuideInfo();
                HttpProxy.instance.getRechargeWithdrawConfig();
            }, (response) => {
                if (!Global.getInstance().is_mini_game()) {
                    this.loadToGame(null);
                } else {
                    if (response && response.code) {
                        cc.log("response.code --------------===>", response.code)
                        if (response.code == MAINTENANCETIPCODE) { //服务器维护
                            showMaintenancetip(response.msg)
                            return;
                        }
                        //增加gcash渠道
                        if (maya_auth_code || gcash_auth_code || g_auth_code || w_auth_code) {
                            if (response.code == 1) {
                                cc.director.emit("showLoginErrMsg", response.msg)
                                Global.getInstance().showCommonTip(response.msg, this, true);
                            } else if (response.code == "103034") {
                                let errStr = Global.getInstance().getLabel("php_code_" + response.code)
                                cc.director.emit("showLoginErrMsg", errStr)
                                Global.getInstance().showCommonTip(errStr, this, true);
                            } else if (response.code == "102024" || response.code == "102006") {
                                let errStr = Global.getInstance().getLabel("php_code_" + response.code)
                                Global.getInstance().showCommonTip2({ word: errStr, confirm: "Customer service" }, this, false, () => {
                                    // ZendeskManager.instance().openZenDesk_Ori()
                                });
                            } else if (response.code == LOCK_ACCOUNT || response.code == 101013 || response.code == 101014) {
                                this.showLock(response)
                            } else if (response.code == "102042" || response.code == "102045") {
                                Global.getInstance().showCommonTip2({ word: response.msg, confirm: "Customer service" }, this, false, () => {
                                    // ZendeskManager.instance().openZenDesk_Ori()
                                });
                            } else if (response.code == '100010') {//一次性令牌过期
                                console.log('-----这里执行几次了')
                                this.autoLogin()//用token登录
                            } else {
                                let errStr = Global.getInstance().getLabel("php_code_" + response.code)
                                cc.director.emit("showLoginErrMsg", errStr)
                                Global.getInstance().showCommonTip(errStr, this, true);
                            }
                        }
                    }
                }
            });
    }
    onClickCustorm() {
        serviceMgr.instance.show_achat(SERVICE_TYPE.coustom)
    }

    onEventMaintenanceTry() {
        let gcash_auth_code = utils.getBrowserValue("gcash_auth_code");
        let g_auth_code = utils.getBrowserValue("g_auth_code");
        let w_auth_code = utils.getBrowserValue("w_auth_code");
        if (gcash_auth_code || g_auth_code || w_auth_code) {
            this.autoLogin()
            return
        }
        let maya_auth_code = utils.getBrowserValue("sessionId");
        if (maya_auth_code) {
            this.autoLogin()
            return
        }
        let token = Global.getInstance().getStoreageData(Global.GLOBAL_STORAGE_KEY.SESSION_ID);
        if (!!token) {
            this.autoLogin()
        }
    }
}

