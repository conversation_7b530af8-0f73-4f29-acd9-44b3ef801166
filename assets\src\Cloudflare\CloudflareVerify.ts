import { ALL_APP_SOURCE_CONFIG } from "../Config";

const { ccclass, property } = cc._decorator;

@ccclass
export default class CloudflareVerify extends cc.Component {
    @property(cc.WebView)
    webView: cc.WebView = null

    /** token 回调 */
    private _tokenCB: Function = null;
    public set tokenCB(value: Function) {
        this._tokenCB = value;
    }

    /** 消息处理函数 */
    private _messageHandler = null;

    /** 是否可中断 */
    private _canInterrupt = false;

    /** 失败自动关闭界面 */
    private _autoCloseWhenErr = false;

    protected onLoad(): void {
        this.scheduleOnce(this._interruptTimerCB, 10);
    }

    /**
     * 显示弹窗
     * @param siteKey 站点密钥
     * @param autoCloseWhenErr 失败自动关闭
     * @param appearance 显示样式
     */
    public show(siteKey: string, appearance = 'always', autoCloseWhenErr = false) {
        this._autoCloseWhenErr = autoCloseWhenErr;

        // 设置原生平台的 JavaScript 接口方案
        if (cc.sys.isNative) {
            this.webView.setJavascriptInterfaceScheme('turnstile');
            this.webView.setOnJSCallback(this.onJSCallback.bind(this));
        }

        const os = cc.sys.os === cc.sys.OS_ANDROID ? 'android' : 
                cc.sys.os === cc.sys.OS_IOS ? 'ios' : 'web';

        let mode = window['debug_mode_main'];
        if(!mode) mode = 'PRE';
        const pageHost = ALL_APP_SOURCE_CONFIG.ActivityPageHost[mode];
        this.webView.url = `${pageHost}turnstile.html?siteKey=${encodeURIComponent(siteKey)}&appearance=${appearance}&isNative=${cc.sys.isNative ? 1 : 0}&os=${os}`;

        this.webView.node.on('loaded', this.onWebLoaded, this);
        this.webView.node.on('error', this.onWebError, this);

        // 只在 Web 平台监听 iframe 消息
        if (cc.sys.isBrowser) {
            this._messageHandler = this.onMessageHandler.bind(this);
            window.addEventListener('message', this._messageHandler);
        }
    }

    onDestroy() {
        this.node.off('loaded', this.onWebLoaded, this);
        this.node.off('error', this.onWebError, this);
        
        // 移除 Web 平台的消息监听
        if (cc.sys.isBrowser && this._messageHandler) {
            window.removeEventListener('message', this._messageHandler);
        }
    }

    /**
     * 消息事件处理
     * @param event 消息事件
     */
    private onMessageHandler(event) {
        if (!cc.isValid(this?.node, true)) {
            return;
        }

        if (event.data.type === 'onTurnstileSuccess') {
            this.onVerifySuccess(event.data.token);
        } else if (event.data.type === 'onTurnstileError') {
            this.onVerifyFailed();
        } else if (event.data.type === 'onTurnstileTimeout') {
            this.onVerifyFailed();
        } else if (event.data.type === 'onTurnstileUnsupported') {
            this.onVerifyFailed();
        }
    }

    /** 原生平台 JavaScript 回调处理 */
    private onJSCallback(_target: any, url: string) {
        if (!cc.isValid(this?.node, true)) {
            return;
        }

        // 手动解析 URL 参数
        const params = this.parseUrlParams(url);
        const action = params.action;
        const token = params.token;
        const errCode = params.errCode;
        
        console.log('CloudflareVerify onJSCallback ', action, token || '', errCode || '');
        
        switch (action) {
            case 'success':
                this.onVerifySuccess(token);
                break;
            case 'error':
                this.onVerifyFailed();
                break;
            case 'timeout':
                this.onVerifyFailed();
                break;
            case 'unsupported':
                this.onVerifyFailed();
                break;
        }
    }

    /**
     * 手动解析URL参数
     * @param url URL字符串
     * @returns 包含参数的普通对象
     */
    private parseUrlParams(url: string): any {
        const params: any = {};
        
        try {
            // 查找问号位置
            const questionMarkIndex = url.indexOf('?');
            if (questionMarkIndex === -1) {
                return params;
            }
            
            // 提取查询字符串
            const queryString = url.substring(questionMarkIndex + 1);
            
            // 分割参数
            const pairs = queryString.split('&');
            for (const pair of pairs) {
                const equalIndex = pair.indexOf('=');
                if (equalIndex !== -1) {
                    const key = pair.substring(0, equalIndex);
                    const value = pair.substring(equalIndex + 1);
                    if (key && value) {
                        try {
                            params[decodeURIComponent(key)] = decodeURIComponent(value);
                        } catch (e) {
                            // 如果解码失败，使用原始值
                            params[key] = value;
                        }
                    }
                }
            }
        } catch (error) {
            console.error('CloudflareVerify parseUrlParams error: ', error);
        }
        
        return params;
    }

    // 创建 Blob URL 避免跨域问题
    private createHtmlBlobUrl(htmlText: string): string {
        const blob = new Blob([htmlText], { type: 'text/html' });
        return URL.createObjectURL(blob);
    }

    /** 网页加载成功 */
    private onWebLoaded() {
        const platformInfo = {
            isNative: cc.sys.isNative,
            os: cc.sys.os === cc.sys.OS_ANDROID ? 'android' : 
                cc.sys.os === cc.sys.OS_IOS ? 'ios' : 'web'
        };

        if (platformInfo.isNative && platformInfo.os === 'android') {
            this.webView.node.width = 308;
            this.webView.node.height = 73;
            this.webView.node.scale = 3.0;
        }
    }

    /** 网页加载失败 */
    private onWebError(err) {
        console.error('CloudflareVerify onWebFinishError: ', err);
        this._tokenCB?.('');
        this.node.destroy();
    }

    /**
     * 验证成功
     * @param token 
     */
    private onVerifySuccess(token: string) {
        console.log('CloudflareVerify onVerifySuccess ', token);
        this._cancelInterruptTimer();
        this.scheduleOnce(() => {
            if (!cc.isValid(this?.node, true)) {
                return;
            }
            this._tokenCB?.(token);
            this.node.destroy();
        }, 1);
    }

    /** 验证失败 */
    private onVerifyFailed() {
        console.log('CloudflareVerify onVerifyFailed ');
        this._cancelInterruptTimer();
        if (this._autoCloseWhenErr) {
            this._tokenCB?.('');
            this.node.destroy();
        }
    }

    /** 背景点击事件 */
    private onBgClick() {
        if (!cc.isValid(this?.node, true)) {
            return;
        }
        if (!this._canInterrupt) {
            return;
        }
        this._tokenCB?.('');
        this.node.destroy();
    }

    /**
     * 取消中断计时器
     */
    private _cancelInterruptTimer() {
        this.unschedule(this._interruptTimerCB);
        this._canInterrupt = true;
    }

    /**
     * 中断计时器回调
     */
    private _interruptTimerCB() {
        this._canInterrupt = true;
    }
}
