import { ALL_APP_SOURCE_CONFIG } from "../Config";
import { GameData } from "../data/GameData";
import { DownloadTipInfo } from "../data/GameDataConst";
import { E_CHANEL_TYPE, EVENT } from "../GlobalConstant";
import Global from "../GlobalScript";
import { NetRspMsg, NetRspObject } from "./Http";
import NetService from "./NetService";

export default class HttpProxy {
    protected static _instance: HttpProxy = null!;
    public static get instance() { return this._instance || (this._instance = new HttpProxy()); }

    get gameData() {
        return GameData.instance;
    }

    // 获取自定义排行榜活动列表
    getChampionshipList() {
        const cmdName = "/bmp-activity/v1/custom-leaderboard/tournament-list";
        const params = { token: Global.getInstance().token };
        const options = {
            showLoading: true,
        }
        const pro = NetService.instance.getRequest(cmdName, params, options);
        pro.then((rsp: NetRspObject) => {
            if (rsp.msg == NetRspMsg.Success) {
                // dispatch(EVENT.PLAYER_GAME_DISTRIBUTION);
            }
            else {
                Global.getInstance().showSimpleTip(rsp.msg);
            }
        });

        return pro;
    }

    // 获取活动详情
    getChampionshipDetails(params) {
        const cmdName = "/bmp-activity/v1/custom-leaderboard/details";
        params.token = Global.getInstance().token;
        const options = {
            showLoading: true,
        }
        const pro = NetService.instance.getRequest(cmdName, params, options);
        pro.then((rsp: NetRspObject) => {
            if (rsp.msg == NetRspMsg.Success) {
                dispatch(EVENT.CHAMPIONSHIP_DETAIL, rsp.data);
            }
            else {
                Global.getInstance().showSimpleTip(rsp.msg);
            }
        });
    }

    // 获取排行榜列表
    getChampionshipRankList(params) {
        const cmdName = "/bmp-activity/v1/custom-leaderboard/leaderboard-list";
        params.token = Global.getInstance().token;
        const options = {
            showLoading: true,
        }
        const pro = NetService.instance.getRequest(cmdName, params, options);
        pro.then((rsp: NetRspObject) => {
            if (rsp.msg == NetRspMsg.Success) {
                dispatch(EVENT.CHAMPIONSHIP_RANK, rsp.data);
            }
            else {
                Global.getInstance().showSimpleTip(rsp.msg);
            }
        });

        return pro;
    }

    // 获取玩家投注汇总
    getPiechartData(params) {
        const cmdName = "/bmp-activity/v1/custom-leaderboard/player-game-distribution";
        params.token = Global.getInstance().token;
        const options = {
            showLoading: true,
        }
        const pro = NetService.instance.getRequest(cmdName, params, options);
        pro.then((rsp: NetRspObject) => {
            if (rsp.msg == NetRspMsg.Success) {
                dispatch(EVENT.PLAYER_GAME_DISTRIBUTION, rsp.data);
            }
            else {
                Global.getInstance().showSimpleTip(rsp.msg);
            }
        });

        return pro;
    }

    // 获取玩家游戏分布
    getRadarData(params) {
        const cmdName = "/bmp-activity/v1/custom-leaderboard/player-betting-summary";
        params.token = Global.getInstance().token;
        const options = {
            showLoading: true,
        }
        const pro = NetService.instance.getRequest(cmdName, params, options);
        pro.then((rsp: NetRspObject) => {
            if (rsp.msg == NetRspMsg.Success) {
                dispatch(EVENT.PLAYER_BETTING_SUMMARY, rsp.data);
            }
            else {
                Global.getInstance().showSimpleTip(rsp.msg);
            }
        });

        return pro;
    }

    /**
     * 获取奖金钱包信息
     * @returns 
     */
    getWalletTaskInfo() {
        const cmdName = "/open/api/wallet/list";
        const options = {
            showLoading: true,
        }
        const pro = NetService.instance.getRequest(cmdName, {}, options);
        pro.then((rsp: NetRspObject) => {
            if (rsp.msg == NetRspMsg.Success) {
                this.gameData.setWalletTaskData(rsp.data);
                dispatch(EVENT.UPDATE_WALLETTASKINFO);
            }
            else {
                Global.getInstance().showSimpleTip(rsp.msg);
            }
        });

        return pro;
    }

    /**
     * 领取任务
     * @param taskId 任务id
     * @returns 
     */
    receiveWalletTask(taskId: string) {
        const cmdName = "/open/api/wallet/receive/task";
        const params = {
            task_id: taskId,
            user_id: Global.getInstance().userdata.user_id
        };
        const options = {
            showLoading: true,
            jsonContentType: true,
        }
        const pro = NetService.instance.postRequest(cmdName, params, options);
        pro.then((rsp: NetRspObject) => {
            if (rsp.msg == NetRspMsg.Success) {
                this.gameData.receiveWalletTask(taskId);
                dispatch(EVENT.UPDATE_WALLETTASKINFO);
            }
            else {
                Global.getInstance().showSimpleTip(rsp.msg);
            }
        });

        return pro;
    }

    /**
     * 领取奖励
     * @param taskId 任务id
     * @returns 
     */
    receiveWalletTaskReward(taskId: string) {
        const cmdName = "/open/api/wallet/receive/reward";
        const params = {
            task_id: taskId,
            user_id: Global.getInstance().userdata.user_id
        }
        const options = {
            showLoading: true,
            jsonContentType: true,
        }
        const pro = NetService.instance.postRequest(cmdName, params, options);
        pro.then((rsp: NetRspObject) => {
            if (rsp.msg == NetRspMsg.Success) {
                this.gameData.receiveWalletTaskReward(taskId);
                dispatch(EVENT.UPDATE_WALLETTASKINFO);
            }
            else {
                Global.getInstance().showSimpleTip(rsp.msg);
            }
        });

        return pro;
    }

    /**
     * 通过渠道获取下载引导
     * @returns 
     */
    getDownloadGuideInfo() {
        if (this.gameData.downloadTipData) {
            dispatch(EVENT.UPDATE_DOWNLOAD_TIP);
            return;
        }

        const cmdName = "/open/api/base/guide";
        const pro = NetService.instance.getRequest(cmdName);
        pro.then((rsp: NetRspObject) => {
            if (rsp.msg == NetRspMsg.Success) {
                this.gameData.downloadTipData = rsp.data;
                dispatch(EVENT.UPDATE_DOWNLOAD_TIP);

                Global.getInstance().setStoreageData(Global.GLOBAL_STORAGE_KEY.DOWNLOAD_TIP, Global.getInstance().now() + "_false");
            }
            else {
                Global.getInstance().showSimpleTip(rsp.msg);
            }
        });

        return pro;
    }

    getRechargeWithdrawConfig() {
        const cmdName = "/common/api/global-config/recharge-withdraw";
        const channel = ALL_APP_SOURCE_CONFIG.channel == E_CHANEL_TYPE.G_CASH ? 'Gcash_h5' : ALL_APP_SOURCE_CONFIG.channel;
        const params = {
            token: Global.getInstance().token,
            appChannel: channel
        };
        const options = {
            tryCount: 3
        }
        const pro = NetService.instance.postRequest(cmdName, params, options);
        pro.then((rsp: NetRspObject) => {
            if (rsp.msg == NetRspMsg.Success) {
                this.gameData.rechargeWithdrawConfig = rsp.data;
                // dispatch(EVENT.UPDATE_DOWNLOAD_TIP);
            }
            else {
                Global.getInstance().showSimpleTip(rsp.msg);
            }
        });

        return pro;
    }

    // 获取
    getGcashToken() {
        const cmdName = "/common/api/player/login/gcash_token";
        const params = { token: Global.getInstance().token };
        const options = {
            showLoading: true,
        }
        const pro = NetService.instance.postRequest(cmdName, params, options);
        pro.then((rsp: NetRspObject) => {
            if (rsp.code == 200) {
                dispatch(EVENT.GCASH_TOKEN, rsp.data);
            }
            else {
                Global.getInstance().showSimpleTip(rsp.msg);
            }
        });

        return pro;
    }

    // 通过渠道获取下载引导
    getGlobalConfig() {
        const cmdName = "/open/api/base/globalConfig";
        const options = { ignoreToken: true };
        const pro = NetService.instance.getRequest(cmdName, {}, options);
        pro.then((rsp: NetRspObject) => {
            if (rsp.msg == NetRspMsg.Success) {
                this.gameData.globalConfig = rsp.data.value;
            }
            else {
                Global.getInstance().showSimpleTip(rsp.msg);
            }
        });

        return pro;
    }

    // 获取强更配置
    getForceConfig() {
        const cmdName = "/open/api/base/force";
        const options = { ignoreToken: true };
        const pro = NetService.instance.getRequest(cmdName, {}, options);
        pro.then((rsp: NetRspObject) => {
            if (rsp.msg == NetRspMsg.Success) {
                this.gameData.forceConfig = rsp.data;
            }
            else {
                Global.getInstance().showSimpleTip(rsp.msg);
            }
        });

        return pro;
    }
}
