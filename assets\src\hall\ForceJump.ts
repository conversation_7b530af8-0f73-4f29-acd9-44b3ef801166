import UICommon from "../component/UICommon";
import { ALL_APP_SOURCE_CONFIG } from "../Config";
import { UIComponent } from "../customComponent/UIComponent";
import { CHANEL_PARAM, DEEP_INDEXZ, E_CHANEL_TYPE, EVENT, UI_PATH_DIC } from "../GlobalConstant";
import Global from "../GlobalScript";
import HttpProxy from "../net/HttpProxy";

const { ccclass, property } = cc._decorator;

export function showForceJumpPop() {
    // 显示 loading
    Global.getInstance().showLoading('ForceJump');

    // 加载弹窗
    cc.resources.load(UI_PATH_DIC.ForceJumpPop, (err: Error, prefab: cc.Prefab) => {
        // 隐藏 loading
        Global.getInstance().hideShowLoading('ForceJump');

        if (!prefab) {
            // 预制体加载失败，飘字提醒
            Global.getInstance().showSimpleTip(Global.getInstance().getLabel("tipword101"));
            return;
        }

        let parent = Global.getInstance().popNode
        if (!parent) {
            // 弹窗父节点不存在
            return;
        }

        if (parent.getChildByName('ForceJump')) {
            // 弹窗已存在
            return;
        }

        // 创建并显示弹窗
        const node = cc.instantiate(prefab);
        node.name = 'ForceJump';
        parent.addChild(node);
        node.zIndex = DEEP_INDEXZ.MAX;
    });
}

@ccclass
export default class ForceJump extends UIComponent {
    @property(cc.Node)
    bgMaya0: cc.Node = null;

    @property(cc.Node)
    bgMaya1: cc.Node = null;

    @property(cc.Node)
    bgGcash0: cc.Node = null;

    @property(cc.Node)
    bgGcash1: cc.Node = null;

    onLoad(): void {
        super.onLoad();
        this.bgMaya0.active = ALL_APP_SOURCE_CONFIG.channel == E_CHANEL_TYPE.MAYA && Global.instance.onlyWebButton != 1;
        this.bgMaya1.active = ALL_APP_SOURCE_CONFIG.channel == E_CHANEL_TYPE.MAYA && Global.instance.onlyWebButton == 1;
        this.bgGcash0.active = ALL_APP_SOURCE_CONFIG.channel == E_CHANEL_TYPE.G_CASH && Global.instance.onlyWebButton != 1;
        this.bgGcash1.active = ALL_APP_SOURCE_CONFIG.channel == E_CHANEL_TYPE.G_CASH && Global.instance.onlyWebButton == 1;

    }

    addEvents(): void {
        this.addEvent(EVENT.GCASH_TOKEN, this.initGcashToken);
    }

    initGcashToken(token){
        let url = Global.DEBUG == Global.DEBUG_MODE.RELEASE ? "https://nustargame.com/" : "https://web.nustaronline.vip/"
        let formatUrl = `${url}?w_auth_code=${token}`;
        console.log('跳转地址:', formatUrl);
        setTimeout(() => {
            cc.sys.openURL(formatUrl);
        });
    }


    onJumpClick1() {
        HttpProxy.instance.getGcashToken(); 
    }

    onJumpClick2() {
        const url = "https://webresult.nustargame.com/download";
        let formatUrl = url;
        formatUrl = `${url}?channel=${CHANEL_PARAM[ALL_APP_SOURCE_CONFIG.channel]}`;

        setTimeout(() => {
            cc.sys.openURL(formatUrl)
        });
    }

    onCopyClick1() {
        const url = "https://nustargame.com/";

        Global.getInstance().setPasteboard(url);
        Global.getInstance().showSimpleTip("Link copied successfully!");
    }

    onCopyClick2() {
        const url = "https://webresult.nustargame.com/download";
        let formatUrl = url;
        formatUrl = `${url}?channel=${CHANEL_PARAM[ALL_APP_SOURCE_CONFIG.channel]}`;

        Global.getInstance().setPasteboard(formatUrl);
        Global.getInstance().showSimpleTip("Link copied successfully!");
    }
}
