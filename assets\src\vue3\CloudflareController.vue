<template>
  <div v-if="showVerifyDialog" class="cloudflare-verify-overlay">
    <div class="verify-dialog">
      <div class="verify-header">
        <h3>{{ $t('security_verification') }}</h3>
        <button @click="closeVerify" class="close-btn">×</button>
      </div>
      <div class="verify-content">
        <iframe
          ref="verifyFrame"
          :src="verifyUrl"
          class="verify-iframe"
          @load="onFrameLoad"
          @error="onFrameError"
        ></iframe>
      </div>
      <div v-if="showError" class="error-message">
        {{ errorMessage }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useGlobalStore } from '@/stores/global'

// Types
export enum CloudFlareScene {
  NONE = '',
  LOGIN_PHONE_GET_CODE = 'SCENE_GET_CODE',
  LOGIN_SUBMIT = 'SCENE_LOGIN',
  FORGET_PW_GET_CODE = 'SCENE_FORGET_PW_GET_CODE',
  FORGET_PW_SUBMIT = 'SCENE_FORGET_PASSWORD',
  FIRST_SET_LOGIN_PW = 'SCENE_FIRST_PASSWORD',
  FIRST_SET_PAY_PW = 'SCENE_FIRST_PAY_PASSWORD',
  MODIFY_LOGIN_PW_GET_CODE = 'SCENE_MODIFY_LOGIN_PW_GET_CODE',
  MODIFY_LOGIN_PW_SUBMIT = 'SCENE_CHANGE_PASSWORD',
  MODIFY_PAY_PW_GET_CODE = 'xxx',
  MODIFY_PAY_PW_SUBMIT = 'SCENE_CHANGE_PAY_PASSWORD',
  BIND_WITHDRAWAL_ACCOUNT_GET_CODE = 'xxx',
  BIND_WITHDRAWAL_ACCOUNT_SUBMIT = 'SCENE_BIND_WITHDRAW_ACCOUNT',
  MODIFY_WITHDRAWAL_ACCOUNT_GET_CODE = 'xxx',
  MODIFY_WITHDRAWAL_ACCOUNT_SUBMIT = 'SCENE_CHANGE_WITHDRAW_ACCOUNT',
  WITHDRAWAL_SUBMIT = 'SCENE_WITHDRAW',
  BIND_PHONE_GET_CODE = 'xxx',
  BIND_PHONE_SUBMIT = 'SCENE_BIND_PT_PHONE',
  MODIFY_PHONE_GET_CODE = 'SCENE_MODIFY_PHONE_GET_CODE',
  MODIFY_PHONE_SUBMIT = 'SCENE_CHANGE_PT_PHONE',
  KYC_SUBMIT = 'SCENE_SUB_KYC_INFO'
}

interface CloudflareInfo {
  mode: string
  sitekey: string
}

interface VerifyResult {
  token: string
  code: number
}

// Props
interface Props {
  config?: {
    ActivityPageHost: Record<string, string>
    CloudflareVerify_SITE_KEY: Record<string, string>
    debug_mode_main: string
  }
}

const props = withDefaults(defineProps<Props>(), {
  config: () => ({
    ActivityPageHost: {
      PRE: 'https://example.com/',
      DEV: 'https://dev.example.com/',
      TEST: 'https://test.example.com/'
    },
    CloudflareVerify_SITE_KEY: {
      PRE: '0x4AAAAAABr6liO_iAPr4Zx_',
      DEV: '0x4AAAAAABr6liO_iAPr4Zx_',
      TEST: '0x4AAAAAABr6liO_iAPr4Zx_'
    },
    debug_mode_main: 'PRE'
  })
})

// Composables
const { t } = useI18n()
const globalStore = useGlobalStore()

// Reactive state
const state = reactive({
  lastScene: CloudFlareScene.NONE,
  lastToken: '',
  cfInfoMap: {
    "SCENE_LOGIN": {
      mode: "managed",
      sitekey: "0x4AAAAAABr6liO_iAPr4Zx_"
    },
    "SCENE_GET_CODE": {
      mode: "invisible", 
      sitekey: "0x4AAAAAABr6n02z8VbwKkph"
    }
  } as Record<string, CloudflareInfo>,
  operationLocked: false,
  lockTimeout: null as NodeJS.Timeout | null
})

// Refs
const showVerifyDialog = ref(false)
const verifyFrame = ref<HTMLIFrameElement>()
const verifyUrl = ref('')
const showError = ref(false)
const errorMessage = ref('')
const currentCallback = ref<((result: VerifyResult) => void) | null>(null)
const messageHandler = ref<((event: MessageEvent) => void) | null>(null)
const interruptTimer = ref<NodeJS.Timeout | null>(null)
const canInterrupt = ref(false)
const autoCloseWhenErr = ref(false)

// Computed
const isOperationLocked = computed(() => state.operationLocked)

// Methods
const setOperationLock = (locked: boolean, duration = 3000) => {
  state.operationLocked = locked
  
  if (state.lockTimeout) {
    clearTimeout(state.lockTimeout)
    state.lockTimeout = null
  }
  
  if (locked) {
    state.lockTimeout = setTimeout(() => {
      state.operationLocked = false
      state.lockTimeout = null
    }, duration)
  }
}

const consumeToken = () => {
  if (state.lastScene && state.lastToken) {
    const result = { scene: state.lastScene, token: state.lastToken }
    state.lastScene = CloudFlareScene.NONE
    state.lastToken = ''
    return result
  }
  return null
}

const getVerifyToken = async (scene: CloudFlareScene): Promise<VerifyResult> => {
  return new Promise((resolve) => {
    if (isOperationLocked.value) {
      resolve({ token: '', code: 1 })
      return
    }

    if (showVerifyDialog.value) {
      resolve({ token: '', code: 1 })
      return
    }

    // Lock operation
    setOperationLock(true)

    // Clear previous token data
    state.lastScene = CloudFlareScene.NONE
    state.lastToken = ''

    // Set current callback
    currentCallback.value = resolve

    // Get cloudflare info
    const cfInfo = state.cfInfoMap[scene]
    if (!cfInfo) {
      setOperationLock(false)
      globalStore.showTip(t('verification_failed'))
      resolve({ token: '', code: 1 })
      return
    }

    const appearance = 'always'
    autoCloseWhenErr.value = cfInfo.mode === 'invisible'

    // Build verify URL
    const mode = props.config.debug_mode_main || 'PRE'
    const pageHost = props.config.ActivityPageHost[mode]
    const os = navigator.platform.toLowerCase().includes('win') ? 'windows' : 
              navigator.platform.toLowerCase().includes('mac') ? 'macos' : 'linux'
    
    verifyUrl.value = `${pageHost}turnstile.html?siteKey=${encodeURIComponent(cfInfo.sitekey)}&appearance=${appearance}&isNative=0&os=${os}`

    // Show dialog
    showVerifyDialog.value = true
    showError.value = false
    errorMessage.value = ''

    // Set interrupt timer
    interruptTimer.value = setTimeout(() => {
      canInterrupt.value = true
    }, 10000)
  })
}

const onMessageHandler = (event: MessageEvent) => {
  if (event.data.type === 'onTurnstileSuccess') {
    onVerifySuccess(event.data.token)
  } else if (event.data.type === 'onTurnstileError') {
    onVerifyFailed()
  } else if (event.data.type === 'onTurnstileTimeout') {
    onVerifyFailed()
  } else if (event.data.type === 'onTurnstileUnsupported') {
    onVerifyFailed()
  }
}

const onVerifySuccess = (token: string) => {
  console.log('CloudflareVerify onVerifySuccess', token)
  cancelInterruptTimer()
  
  setTimeout(() => {
    state.lastScene = state.lastScene
    state.lastToken = token
    
    currentCallback.value?.({ token, code: token ? 0 : 2 })
    closeVerify()
    setOperationLock(false)
  }, 100)
}

const onVerifyFailed = () => {
  console.log('CloudflareVerify onVerifyFailed')
  cancelInterruptTimer()
  
  if (autoCloseWhenErr.value) {
    currentCallback.value?.({ token: '', code: 2 })
    closeVerify()
  } else {
    showError.value = true
    errorMessage.value = t('verification_failed')
  }
  setOperationLock(false)
}

const onFrameLoad = () => {
  console.log('Cloudflare verify frame loaded')
}

const onFrameError = () => {
  console.log('Cloudflare verify frame error')
  onVerifyFailed()
}

const closeVerify = () => {
  if (!canInterrupt.value && showVerifyDialog.value) {
    return
  }
  
  showVerifyDialog.value = false
  showError.value = false
  errorMessage.value = ''
  
  if (currentCallback.value) {
    currentCallback.value({ token: '', code: 2 })
    currentCallback.value = null
  }
  
  cancelInterruptTimer()
  setOperationLock(false)
}

const cancelInterruptTimer = () => {
  if (interruptTimer.value) {
    clearTimeout(interruptTimer.value)
    interruptTimer.value = null
  }
  canInterrupt.value = true
}

// Lifecycle
onMounted(() => {
  messageHandler.value = onMessageHandler
  window.addEventListener('message', messageHandler.value)
})

onUnmounted(() => {
  if (messageHandler.value) {
    window.removeEventListener('message', messageHandler.value)
  }
  cancelInterruptTimer()
  if (state.lockTimeout) {
    clearTimeout(state.lockTimeout)
  }
})

// Expose methods
defineExpose({
  getVerifyToken,
  consumeToken,
  lastScene: computed(() => state.lastScene),
  lastToken: computed(() => state.lastToken)
})
</script>

<style scoped>
.cloudflare-verify-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.verify-dialog {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  max-height: 80%;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.verify-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #eee;
}

.verify-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  color: #333;
}

.verify-content {
  padding: 20px;
  min-height: 300px;
}

.verify-iframe {
  width: 100%;
  height: 300px;
  border: none;
  border-radius: 4px;
}

.error-message {
  padding: 16px 20px;
  background-color: #fee;
  color: #c33;
  border-top: 1px solid #eee;
  text-align: center;
}
</style>
